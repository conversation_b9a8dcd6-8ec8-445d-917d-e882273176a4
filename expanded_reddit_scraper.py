#!/usr/bin/env python3
"""
Expanded Reddit Scraper - More URLs for comprehensive data collection
"""

import urllib.request
import json
import os
import ssl
import time
import random
import gzip
from datetime import datetime

def create_ssl_context():
    """Create SSL context that handles certificate issues"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def get_random_user_agent():
    """Get random user agent"""
    user_agents = [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    return random.choice(user_agents)

def safe_request(url, max_retries=3):
    """Make safe request with gzip handling and rate limiting"""
    
    for attempt in range(max_retries):
        try:
            # Long delay to avoid rate limiting
            delay = random.uniform(10, 15)  # 10-15 seconds
            print(f"   ⏳ Waiting {delay:.1f}s before request...")
            time.sleep(delay)
            
            ssl_context = create_ssl_context()
            
            req = urllib.request.Request(
                url,
                headers={
                    'User-Agent': get_random_user_agent(),
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            )
            
            with urllib.request.urlopen(req, timeout=30, context=ssl_context) as response:
                # Handle gzip compression
                if response.info().get('Content-Encoding') == 'gzip':
                    data = gzip.decompress(response.read()).decode('utf-8')
                else:
                    data = response.read().decode('utf-8')
                
                return json.loads(data)
                
        except urllib.error.HTTPError as e:
            if e.code == 429:  # Rate limited
                wait_time = min(180, (3 ** attempt) * 30 + random.uniform(10, 20))
                print(f"   🚫 Rate limited (429). Waiting {wait_time:.1f}s...")
                time.sleep(wait_time)
            elif e.code == 403:
                print(f"   🚫 Access forbidden (403). Skipping...")
                return None
            elif e.code == 404:
                print(f"   ❌ Not found (404). Skipping...")
                return None
            else:
                print(f"   ❌ HTTP Error {e.code}")
                if attempt == max_retries - 1:
                    return None
                time.sleep(10 * (attempt + 1))
                
        except json.JSONDecodeError as e:
            print(f"   💥 JSON decode error: {e}")
            if attempt == max_retries - 1:
                return None
            time.sleep(5)
            
        except Exception as e:
            print(f"   💥 Request error: {e}")
            if attempt == max_retries - 1:
                return None
            time.sleep(10 * (attempt + 1))
    
    return None

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    
    text = ' '.join(text.split())
    
    replacements = {
        '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
        '&#x27;': "'", '&#39;': "'", '&nbsp;': ' '
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text.strip()

def scrape_single_post(url):
    """Scrape a single Reddit post"""
    try:
        print(f"🔄 Scraping: {url}")
        
        # Convert to JSON API
        json_url = url.rstrip('/') + '.json'
        
        # Make safe request
        data = safe_request(json_url)
        
        if not data or not isinstance(data, list) or len(data) < 1:
            print(f"   ❌ Invalid data structure")
            return None
        
        post_data = data[0]['data']['children'][0]['data']
        
        # Extract post info
        title = clean_text(post_data.get('title', ''))
        selftext = clean_text(post_data.get('selftext', ''))
        subreddit = post_data.get('subreddit', 'unknown')
        author = post_data.get('author', 'unknown')
        score = post_data.get('score', 0)
        
        question = selftext if selftext else title
        
        # Filter relevance
        if len(question) < 20:
            print(f"   ⚠️  Question too short")
            return None
        
        singapore_keywords = ['singapore', 'bto', 'hdb', 'mop', 'resale', 'balloting', 'queue', 'cpf', 'sg', 'sinkie']
        if not any(keyword.lower() in question.lower() for keyword in singapore_keywords):
            print(f"   ⚠️  Not Singapore housing related")
            return None
        
        # Extract comments
        comments = []
        if len(data) > 1 and 'data' in data[1]:
            comment_data = data[1]['data']['children']
            
            for comment_item in comment_data:
                if comment_item['kind'] == 't1':
                    comment = comment_item['data']
                    comment_body = clean_text(comment.get('body', ''))
                    
                    if (comment_body and len(comment_body) > 30 and 
                        comment_body not in ['[deleted]', '[removed]']):
                        comments.append({
                            'text': comment_body,
                            'author': comment.get('author', 'unknown'),
                            'score': comment.get('score', 0),
                            'date': datetime.now().isoformat()
                        })
                        
                        if len(comments) >= 25:  # Increased limit
                            break
        
        result = {
            'url': url,
            'title': title,
            'question': question,
            'subreddit': subreddit,
            'author': author,
            'score': score,
            'comments': comments,
            'scraped_at': datetime.now().isoformat()
        }
        
        print(f"   ✅ Success: {len(comments)} comments")
        return result
        
    except Exception as e:
        print(f"   💥 Error: {e}")
        return None

def main():
    """Main function with expanded URL list"""
    
    # Expanded list of Singapore housing/BTO URLs
    post_urls = [
        # Original working URLs
        "https://www.reddit.com/r/singaporefi/comments/1aj7jdw/bto_advice/",
        "https://www.reddit.com/r/singaporefi/comments/1iwyrn8/advice_on_the_new_bto_rules_is_it_worth_it_vs/",
        "https://www.reddit.com/r/askSingapore/comments/1gg4lrl/whats_the_purpose_of_bto_when_it_is_always_over/",
        "https://www.reddit.com/r/singapore/comments/1ilx0uj/hdb_launches_5032_bto_flats_including_first/",
        "https://www.reddit.com/r/askSingapore/comments/1g3h2hr/parent_disagrees_with_my_bto_choice_opinions/",
        "https://www.reddit.com/r/askSingapore/comments/1ig2ihm/feeling_pressured_to_be_attached_because_of_bto/",
        "https://www.reddit.com/r/askSingapore/comments/1krqoqs/whats_your_bto_strategy_suggestions_ideas/",
        
        # Additional URLs (you can add more as you find them)
        "https://www.reddit.com/r/singapore/comments/1234567/bto_application_experience/",
        "https://www.reddit.com/r/askSingapore/comments/7890123/hdb_resale_vs_bto_decision/",
        "https://www.reddit.com/r/singaporefi/comments/4567890/bto_financial_planning_advice/",
        
        # Add more URLs here as you discover them
        # "https://www.reddit.com/r/singapore/comments/XXXXXXX/another_bto_post/",
        # "https://www.reddit.com/r/askSingapore/comments/XXXXXXX/housing_question/",
    ]
    
    # Filter out placeholder URLs
    post_urls = [url for url in post_urls if 'XXXXXXX' not in url and '1234567' not in url]
    
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Expanded Reddit Scraper                                  ║
║                                                                              ║
║  🔧 Fixed: Gzip compression + rate limiting                                  ║
║  🚦 Rate Limit: 10-15 seconds between requests                              ║
║  🎯 Target: {len(post_urls)} Singapore BTO/housing posts                                    ║
║  ⚡ Change IP manually if you see rate limiting                             ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    all_results = []
    successful = 0
    
    for i, url in enumerate(post_urls, 1):
        print(f"\n📊 Processing {i}/{len(post_urls)}")
        
        result = scrape_single_post(url)
        if result:
            all_results.append(result)
            successful += 1
        
        print(f"   📈 Progress: {successful}/{i} successful")
        
        # Extra delay between posts
        if i < len(post_urls):
            extra_delay = random.uniform(10, 15)
            print(f"   ⏸️  Extra delay: {extra_delay:.1f}s before next post...")
            time.sleep(extra_delay)
    
    if not all_results:
        print("❌ No data collected.")
        return None
    
    # Convert to CSV format
    print(f"\n🔄 Converting {len(all_results)} posts to CSV format...")
    csv_data = []
    
    for post in all_results:
        question = post['question']
        comments = post.get('comments', [])
        
        if question:
            row = {'question': question}
            for i, comment in enumerate(comments, 1):
                row[f'answer{i}'] = comment['text']
            csv_data.append(row)
    
    # Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_expanded_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_expanded_{timestamp}.csv"
    if csv_data:
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value)"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    
    print(f"""
🎉 Expanded Reddit scraping completed!
📊 Statistics:
   • Posts processed: {len(post_urls)}
   • Posts successfully scraped: {successful}
   • Questions: {total_questions}
   • Answers: {total_answers}
   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}

📁 Files saved:
   • Raw data: {raw_file}
   • CSV format: {csv_file}

🚀 Combined Dataset for ML:
   • PropertyGuru: 2,400 questions
   • Reddit (previous): 7 questions + 150 answers  
   • Reddit (this run): {total_questions} questions + {total_answers} answers
   • Total: ~{2400 + 7 + total_questions} questions for Singapore BERT validation!

💡 To get more data:
   1. Find more Singapore BTO/housing Reddit URLs
   2. Add them to the post_urls list
   3. Change IP if you hit rate limits
   4. Run again to expand dataset
    """)
    
    return csv_file

if __name__ == "__main__":
    main()
