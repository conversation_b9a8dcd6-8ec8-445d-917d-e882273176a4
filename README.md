# 🏠 Singapore Housing Data Collection & ML Pipeline

A comprehensive data collection and machine learning pipeline for Singapore housing discussions, combining expert Q&A from PropertyGuru with community discussions from Reddit. **Now with 2,180+ questions and 9,423+ answers ready for ML training!**

## 🏆 **PROJECT ACHIEVEMENTS**

✅ **Comprehensive Data Collection** - 2,180 questions from PropertyGuru + Reddit
✅ **Unified ML Schema** - `question, answer_1, answer_2, ..., answer_25` format
✅ **Production-Ready Scrapers** - Robust scraping with error handling and resume capability
✅ **Clean Codebase** - Organized structure with proper separation of concerns
✅ **ML-Ready Datasets** - Perfect for BERT fine-tuning and answer ranking
✅ **Singapore-Specific** - Focused on BTO, HDB, and housing discussions
✅ **Scalable Architecture** - Easy to extend with new data sources

## 📊 **Current Dataset Statistics**

### **Combined Dataset**: 2,180 Total Questions
- **🏠 PropertyGuru**: 2,056 expert Q&A entries
- **🔍 Reddit**: 124 community discussions
- **📝 Total Answers**: 9,423 individual answer texts
- **📈 Schema**: `question, answer_1, answer_2, ..., answer_25`
- **🎯 Main File**: `data/combined/ALL_COMBINED_SCHEMA_*.csv`

## 🚀 **Quick Start**

### **1. Combine All Data**
```bash
# Combine all PropertyGuru + Reddit data into ML-ready format
python3 combine_all_datasets.py

# Output: data/combined/ALL_COMBINED_SCHEMA_*.csv
```

### **2. Run Individual Scrapers**
```bash
# PropertyGuru scraper (comprehensive)
python3 src/scrapers/propertyguru/distributed_propertyguru_scraper.py

# Reddit scraper
python3 src/scrapers/reddit/reddit_scraper.py

# Main pipeline
python3 src/main.py
```

### **3. Development Tools**
```bash
# Start distributed scraping (3 terminals)
bash tools/start_distributed_scraping.sh

# Test requests
python3 tools/test_requests.py
```

## 🏗️ **Project Structure**

```
├── src/                          # Main source code
│   ├── scrapers/                 # Web scraping modules
│   │   ├── propertyguru/         # PropertyGuru expert Q&A scraper
│   │   ├── reddit/               # Reddit community scraper
│   │   └── hardwarezone/         # HardwareZone forum scraper
│   ├── data_processing/          # Data combination and processing
│   │   └── combine_all_datasets.py
│   └── utils/                    # Utility functions
│       ├── scraper_helper.py     # Common scraping utilities
│       └── gemini_processor.py   # AI processing utilities
├── data/                         # Data storage
│   ├── combined/                 # Final ML-ready datasets ⭐
│   ├── final/                    # PropertyGuru processed data
│   ├── processed/                # Reddit processed data
│   └── archive/                  # Historical data
├── tools/                        # Development tools
│   ├── legacy_scrapers/          # Old scraper versions
│   └── start_distributed_scraping.sh
├── docs/                         # Documentation
├── ml_validation/                # ML pipeline
└── requirements.txt              # Dependencies
```

## 🎯 **Data Flow**

```
PropertyGuru (2,056 Q&A) ──┐
                           ├──► combine_all_datasets.py ──► ML-ready CSV
Reddit (124 discussions) ──┘
```

## 📋 **ML-Ready Output Format**

### **Main Dataset Schema**
```csv
question,source,answer_count,url,title,author,subreddit,score,answer_1,answer_2,...,answer_25
```

### **Individual Scraper Output**
```json
{
  "source": "propertyguru|reddit",
  "question": "main question text",
  "answers": ["answer1", "answer2", "..."],
  "metadata": {
    "url": "source_url",
    "title": "post title",
    "author": "username",
    "date": "timestamp"
  }
}
```

## 🛠️ **Installation & Setup**

```bash
# Clone and setup
cd "MVP for Pearly"
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Optional: Set up Gemini API for text processing
export GEMINI_API_KEY=your_api_key_here

# Run data combination
python3 combine_all_datasets.py
```

## 🚀 **Next Steps**

1. **ML Training**: Use `data/combined/ALL_COMBINED_SCHEMA_*.csv` for BERT fine-tuning
2. **Model Comparison**: Test Singapore BERT vs Baseline BERT
3. **Answer Ranking**: Implement answer quality prediction models
4. **Data Expansion**: Add more Reddit communities and data sources
5. **Production Deployment**: Scale up for real-time data collection