# 🏠 Singapore Housing Data Collection & ML Pipeline

A comprehensive data collection and machine learning pipeline for Singapore housing discussions, combining expert Q&A from PropertyGuru with community discussions from Reddit. **Now with 2,180+ questions and 9,423+ answers ready for ML training!**

## 🏆 **PROJECT ACHIEVEMENTS**

✅ **Comprehensive Data Collection** - 2,180 questions from PropertyGuru + Reddit
✅ **Unified ML Schema** - `question, answer_1, answer_2, ..., answer_25` format
✅ **Production-Ready Scrapers** - Robust scraping with error handling and resume capability
✅ **Clean Codebase** - Organized structure with proper separation of concerns
✅ **ML-Ready Datasets** - Perfect for BERT fine-tuning and answer ranking
✅ **Singapore-Specific** - Focused on BTO, HDB, and housing discussions
✅ **Scalable Architecture** - Easy to extend with new data sources

## 🚀 **NEW: Comprehensive PropertyGuru Scraping**

### **Scale**: All 708 pages (~14,000+ questions with answers)
### **Features**:
- **Resume capability** - Automatically resumes from last completed page
- **Progress tracking** - Saves progress every 50 questions
- **Error recovery** - Retries failed pages with exponential backoff
- **Real-time monitoring** - Track progress with statistics
- **Batch processing** - Efficient memory usage for large datasets
- **Comprehensive logging** - Detailed logs for debugging and monitoring

## 📊 **Quick Start - Comprehensive Scraping**

### **Option 1: Dedicated Comprehensive Scraper**
```bash
# Scrape all 708 pages with resume capability
python comprehensive_propertyguru_scraper.py

# Resume from previous progress
python comprehensive_propertyguru_scraper.py

# Retry only failed pages
python comprehensive_propertyguru_scraper.py --retry-failed

# Check current progress
python comprehensive_propertyguru_scraper.py --stats
```

### **Option 2: Through Main Pipeline**
```bash
# Regular demo mode (5 questions)
python -m mvp_scraper.main

# Comprehensive mode (all 708 pages)
python -m mvp_scraper.main --comprehensive
```

### **Option 3: Monitor Progress**
```bash
# One-time progress check
python monitor_scraping_progress.py

# Continuous monitoring (updates every 30 seconds)
python monitor_scraping_progress.py --continuous

# Custom update interval
python monitor_scraping_progress.py --continuous --interval 60
```

## ⏱️ **Performance Estimates**

### **Comprehensive PropertyGuru Scraping (708 pages)**
- **Estimated Questions**: ~14,000+ questions
- **Estimated Answers**: ~84,000+ answers
- **Estimated Runtime**: 12-24 hours (depending on network conditions)
- **Memory Usage**: Optimized with batching and progress saving
- **Resume Time**: < 30 seconds to resume from any point

### **Progress Tracking**
- Progress saved every 50 questions
- Failed pages tracked and can be retried separately
- Real-time statistics and completion estimates
- Comprehensive logging for monitoring

## 🔧 **Core Architecture**

### **Enhanced Components**
- `mvp_scraper/property_guru_scraper.py` - **ENHANCED** with comprehensive scraping
- `mvp_scraper/reddit_scraper.py` - Improved to match PropertyGuru pattern
- `mvp_scraper/hardware_zone_scraper.py` - Forum thread scraping
- `mvp_scraper/scraper_helper.py` - Robust scraping with crawl4ai/Selenium fallback
- `mvp_scraper/gemini_processor.py` - Text processing via Gemini API
- `mvp_scraper/main.py` - **ENHANCED** Main orchestrator with comprehensive mode
- `comprehensive_propertyguru_scraper.py` - **NEW** Dedicated comprehensive scraper
- `monitor_scraping_progress.py` - **NEW** Progress monitoring tool

### **Success Pattern Established**
```
PropertyGuru: Questions → Answers (ALL 708 pages supported)
Reddit: Posts → Comments
HardwareZone: Topics → Replies
```

## 📋 **Unified Output Format**

All scrapers produce identical structure:
```json
{
  "source": "propertyguru|reddit|hardwarezone",
  "type": "question|post|topic",
  "text": "main content",
  "author": "username",
  "date": "timestamp",
  "url": "source_url",
  "title": "post title",
  "answers|comments|replies": [
    {
      "text": "response content",
      "author": "responder",
      "date": "timestamp"
    }
  ],
  "raw_data": {...}
}
```

## 🛠️ **Installation & Setup**

```bash
# Clone and setup
cd "MVP for Pearly"
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Optional: Set up Gemini API for text processing
export GEMINI_API_KEY=your_api_key_here
```