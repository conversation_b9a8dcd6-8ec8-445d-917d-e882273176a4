#!/usr/bin/env python3
"""
Run Reddit Scraper for Singapore Housing/BTO URLs
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mvp_scraper.reddit_scraper import RedditScraper

async def main():
    """Run the Reddit scraper"""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Reddit Singapore Housing Scraper                         ║
║                                                                              ║
║  🎯 Target URLs: 7 specific Reddit posts about Singapore housing/BTO        ║
║  📊 Output: question, answer1, answer2, ... (unlimited answers)             ║
║  🔄 Ready for ML validation dataset expansion                                ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    scraper = RedditScraper()
    
    try:
        print("🚀 Starting Reddit scraping...")
        
        # Scrape all Reddit URLs
        results = await scraper.scrape_all_reddit_urls()
        
        # Save results
        files = await scraper.save_results(results)
        
        print(f"\n🎉 SUCCESS! Reddit scraping completed!")
        print(f"📊 Statistics:")
        stats = results['statistics']
        print(f"   • Questions collected: {stats['total_questions']}")
        print(f"   • Answers collected: {stats['total_answers']}")
        print(f"   • Max answers per question: {stats['max_answers_per_question']}")
        print(f"   • Successful scrapes: {stats['successful_scrapes']}/7")
        
        print(f"\n📁 Files created:")
        print(f"   • CSV format: {files['csv_file']}")
        print(f"   • Q&A JSON: {files['qa_file']}")
        print(f"   • Raw data: {files['raw_file']}")
        
        print(f"\n🚀 Next steps:")
        print(f"   1. Combine with PropertyGuru data")
        print(f"   2. Create unified ML dataset")
        print(f"   3. Run BERT validation in Google Colab")
        
        return files
        
    except Exception as e:
        print(f"\n💥 Error during Reddit scraping: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n✅ Reddit scraper completed successfully!")
        else:
            print("\n❌ Reddit scraper failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏸️  Scraping interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)
