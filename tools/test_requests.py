#!/usr/bin/env python3
"""
Test requests module
"""

try:
    import sys
    print(f"Python path: {sys.path[:3]}")
    
    import requests
    print(f"Requests module: {requests}")
    print(f"Requests file: {requests.__file__}")
    print(f"Requests version: {requests.__version__}")
    
    # Test a simple GET request
    response = requests.get('https://httpbin.org/get', timeout=10)
    print(f"✅ GET request successful: {response.status_code}")
    
except Exception as e:
    print(f"💥 Error: {e}")
    import traceback
    traceback.print_exc()
