#!/usr/bin/env python3
"""
Reddit Search Scraper - Search for BTO Singapore posts and scrape them in parallel
"""

import urllib.request
import urllib.parse
import json
import os
import ssl
import re
from datetime import datetime
import time
import concurrent.futures
from threading import Lock

# Global variables for thread safety
results_lock = Lock()
all_results = []

def create_ssl_context():
    """Create SSL context that handles certificate issues"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = ' '.join(text.split())
    
    # Remove common escape sequences
    replacements = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#x27;': "'",
        '&#39;': "'",
        '&nbsp;': ' '
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text.strip()

def extract_post_urls_from_search(search_url, max_pages=5):
    """Extract individual post URLs from Reddit search results"""
    print(f"🔍 Searching Reddit for BTO Singapore posts...")
    
    post_urls = set()  # Use set to avoid duplicates
    
    try:
        ssl_context = create_ssl_context()
        
        # Try different search approaches
        search_queries = [
            "https://www.reddit.com/search.json?q=BTO+singapore&sort=relevance&limit=100",
            "https://www.reddit.com/search.json?q=BTO+housing+singapore&sort=relevance&limit=100",
            "https://www.reddit.com/search.json?q=singapore+BTO+advice&sort=relevance&limit=100",
            "https://www.reddit.com/r/singapore/search.json?q=BTO&sort=relevance&limit=100&restrict_sr=1",
            "https://www.reddit.com/r/askSingapore/search.json?q=BTO&sort=relevance&limit=100&restrict_sr=1",
            "https://www.reddit.com/r/singaporefi/search.json?q=BTO&sort=relevance&limit=100&restrict_sr=1"
        ]
        
        for search_query in search_queries:
            try:
                print(f"   🔄 Searching: {search_query}")
                
                req = urllib.request.Request(
                    search_query,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (compatible; RedditScraper/1.0; Educational)',
                        'Accept': 'application/json'
                    }
                )
                
                with urllib.request.urlopen(req, timeout=30, context=ssl_context) as response:
                    data = json.loads(response.read().decode('utf-8'))
                
                # Extract post URLs from search results
                if 'data' in data and 'children' in data['data']:
                    for post in data['data']['children']:
                        if post['kind'] == 't3':  # Post type
                            post_data = post['data']
                            permalink = post_data.get('permalink', '')
                            if permalink:
                                full_url = f"https://www.reddit.com{permalink}"
                                post_urls.add(full_url)
                
                print(f"   ✅ Found {len(post_urls)} unique posts so far")
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                print(f"   ⚠️  Error with search query: {e}")
                continue
    
    except Exception as e:
        print(f"💥 Error in search: {e}")
    
    print(f"🎯 Total unique post URLs found: {len(post_urls)}")
    return list(post_urls)

def scrape_single_post(url):
    """Scrape a single Reddit post using JSON API"""
    try:
        # Convert URL to JSON API endpoint
        json_url = url.rstrip('/') + '.json'
        
        ssl_context = create_ssl_context()
        
        req = urllib.request.Request(
            json_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (compatible; RedditScraper/1.0; Educational)',
                'Accept': 'application/json'
            }
        )
        
        with urllib.request.urlopen(req, timeout=30, context=ssl_context) as response:
            data = json.loads(response.read().decode('utf-8'))
        
        # Extract post data
        if not isinstance(data, list) or len(data) < 1:
            return None
        
        post_data = data[0]['data']['children'][0]['data']
        
        # Extract basic info
        title = clean_text(post_data.get('title', ''))
        selftext = clean_text(post_data.get('selftext', ''))
        subreddit = post_data.get('subreddit', 'unknown')
        author = post_data.get('author', 'unknown')
        score = post_data.get('score', 0)
        
        # Use selftext as question, fallback to title
        question = selftext if selftext else title
        
        # Skip if question is too short or not relevant
        if len(question) < 20:
            return None
        
        # Check if it's actually about Singapore BTO/housing
        singapore_keywords = ['singapore', 'bto', 'hdb', 'mop', 'resale', 'balloting', 'queue']
        if not any(keyword.lower() in question.lower() for keyword in singapore_keywords):
            return None
        
        # Extract comments
        comments = []
        if len(data) > 1 and 'data' in data[1]:
            comment_data = data[1]['data']['children']
            
            for comment_item in comment_data:
                if comment_item['kind'] == 't1':  # Comment type
                    comment = comment_item['data']
                    comment_body = clean_text(comment.get('body', ''))
                    comment_author = comment.get('author', 'unknown')
                    comment_score = comment.get('score', 0)
                    
                    if (comment_body and len(comment_body) > 30 and 
                        comment_body != '[deleted]' and comment_body != '[removed]'):
                        comments.append({
                            'text': comment_body,
                            'author': comment_author,
                            'date': datetime.now().isoformat(),
                            'score': comment_score
                        })
                        
                        if len(comments) >= 25:  # Limit to 25 comments per post
                            break
        
        result = {
            'url': url,
            'title': title,
            'question': question,
            'subreddit': subreddit,
            'author': author,
            'score': score,
            'comments': comments,
            'scraped_at': datetime.now().isoformat()
        }
        
        # Thread-safe result storage
        with results_lock:
            all_results.append(result)
        
        print(f"✅ {url}: {len(comments)} comments")
        return result
        
    except Exception as e:
        print(f"❌ {url}: {e}")
        return None

def parallel_scrape_posts(post_urls, max_workers=3):
    """Scrape posts in parallel with limited workers"""
    print(f"\n🚀 Starting parallel scraping with {max_workers} workers...")
    print(f"📊 Total posts to scrape: {len(post_urls)}")
    
    successful = 0
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_url = {executor.submit(scrape_single_post, url): url for url in post_urls}
        
        # Process completed tasks
        for i, future in enumerate(concurrent.futures.as_completed(future_to_url), 1):
            url = future_to_url[future]
            try:
                result = future.result()
                if result:
                    successful += 1
                
                # Progress update
                if i % 10 == 0 or i == len(post_urls):
                    print(f"   📈 Progress: {i}/{len(post_urls)} ({successful} successful)")
                
            except Exception as e:
                print(f"   💥 Error processing {url}: {e}")
            
            # Rate limiting between requests
            time.sleep(0.5)
    
    print(f"🎉 Parallel scraping completed: {successful}/{len(post_urls)} successful")
    return successful

def convert_to_csv_format(reddit_data):
    """Convert to CSV format"""
    csv_rows = []
    
    for post in reddit_data:
        if not post:
            continue
        
        question = post['question']
        comments = post.get('comments', [])
        
        if not question:
            continue
        
        # Create CSV row
        row = {'question': question}
        
        # Add answers (unlimited)
        for i, comment in enumerate(comments, 1):
            row[f'answer{i}'] = comment['text']
        
        csv_rows.append(row)
    
    return csv_rows

def main():
    """Main function"""
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Reddit Search & Parallel Scraper                         ║
║                                                                              ║
║  🔍 Search: BTO Singapore posts across multiple subreddits                  ║
║  🚀 Method: Parallel processing with JSON API                               ║
║  📊 Output: question, answer1, answer2, ... (unlimited)                     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Step 1: Search for post URLs
    post_urls = extract_post_urls_from_search("BTO singapore")
    
    if not post_urls:
        print("❌ No post URLs found. Exiting.")
        return None
    
    # Limit to reasonable number for testing
    max_posts = min(50, len(post_urls))  # Start with 50 posts
    post_urls = post_urls[:max_posts]
    
    print(f"\n🎯 Selected {len(post_urls)} posts for scraping")
    
    # Step 2: Parallel scrape posts
    successful = parallel_scrape_posts(post_urls, max_workers=3)
    
    if not all_results:
        print("❌ No data collected.")
        return None
    
    # Step 3: Convert to CSV format
    print(f"\n🔄 Converting {len(all_results)} posts to CSV format...")
    csv_data = convert_to_csv_format(all_results)
    
    # Step 4: Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_search_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_search_{timestamp}.csv"
    if csv_data:
        # Find all answer columns
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Headers
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            # Data
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value}"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    max_answers = max([len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data]) if csv_data else 0
    
    print(f"""
🎉 Reddit search scraping completed!
📊 Statistics:
   • Posts found: {len(post_urls)}
   • Posts successfully scraped: {successful}
   • Questions: {total_questions}
   • Answers: {total_answers}
   • Max answers per question: {max_answers}
   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}

📁 Files saved:
   • Raw data: {raw_file}
   • CSV format: {csv_file}

🚀 Ready for ML validation!
   • PropertyGuru: 2,400 questions
   • Reddit (manual): 7 questions + 150 answers
   • Reddit (search): {total_questions} questions + {total_answers} answers
   • Combined: {2400 + 7 + total_questions} questions for Singapore BERT vs Baseline BERT!
    """)
    
    return csv_file

if __name__ == "__main__":
    main()
