#!/usr/bin/env python3
"""
Rate-Limited Reddit Scraper - Avoids getting blocked
"""

import urllib.request
import urllib.parse
import json
import os
import ssl
import time
import random
from datetime import datetime
import concurrent.futures
from threading import Lock

# Rate limiting configuration
REQUESTS_PER_MINUTE = 20  # Conservative limit
MIN_DELAY = 3  # Minimum 3 seconds between requests
MAX_DELAY = 6  # Maximum 6 seconds between requests

# Global rate limiting
request_times = []
rate_limit_lock = Lock()

def create_ssl_context():
    """Create SSL context that handles certificate issues"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def get_random_user_agent():
    """Get random user agent to avoid detection"""
    user_agents = [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15'
    ]
    return random.choice(user_agents)

def rate_limited_request(url, max_retries=3):
    """Make rate-limited request with exponential backoff"""
    global request_times
    
    with rate_limit_lock:
        now = time.time()
        
        # Remove requests older than 1 minute
        request_times = [t for t in request_times if now - t < 60]
        
        # Check if we're hitting rate limit
        if len(request_times) >= REQUESTS_PER_MINUTE:
            sleep_time = 60 - (now - request_times[0]) + random.uniform(1, 3)
            print(f"   ⏳ Rate limit reached. Sleeping {sleep_time:.1f}s...")
            time.sleep(sleep_time)
            request_times = []
        
        # Add current request time
        request_times.append(now)
    
    # Random delay between requests
    delay = random.uniform(MIN_DELAY, MAX_DELAY)
    time.sleep(delay)
    
    # Attempt request with retries
    for attempt in range(max_retries):
        try:
            ssl_context = create_ssl_context()
            
            req = urllib.request.Request(
                url,
                headers={
                    'User-Agent': get_random_user_agent(),
                    'Accept': 'application/json',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'no-cache'
                }
            )
            
            with urllib.request.urlopen(req, timeout=30, context=ssl_context) as response:
                return json.loads(response.read().decode('utf-8'))
                
        except urllib.error.HTTPError as e:
            if e.code == 429:  # Rate limited
                wait_time = min(60, (2 ** attempt) * 10 + random.uniform(0, 5))
                print(f"   🚫 Rate limited (429). Waiting {wait_time:.1f}s...")
                time.sleep(wait_time)
            elif e.code == 403:  # Forbidden
                print(f"   🚫 Access forbidden (403). Skipping...")
                return None
            else:
                print(f"   ❌ HTTP Error {e.code}")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2 ** attempt)
        except Exception as e:
            print(f"   💥 Request error: {e}")
            if attempt == max_retries - 1:
                return None
            time.sleep(2 ** attempt)
    
    return None

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    
    text = ' '.join(text.split())
    
    replacements = {
        '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
        '&#x27;': "'", '&#39;': "'", '&nbsp;': ' '
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text.strip()

def scrape_single_post(url):
    """Scrape a single Reddit post with rate limiting"""
    try:
        print(f"🔄 Scraping: {url}")
        
        # Convert to JSON API
        json_url = url.rstrip('/') + '.json'
        
        # Rate-limited request
        data = rate_limited_request(json_url)
        
        if not data or not isinstance(data, list) or len(data) < 1:
            print(f"   ❌ Invalid data structure")
            return None
        
        post_data = data[0]['data']['children'][0]['data']
        
        # Extract post info
        title = clean_text(post_data.get('title', ''))
        selftext = clean_text(post_data.get('selftext', ''))
        subreddit = post_data.get('subreddit', 'unknown')
        author = post_data.get('author', 'unknown')
        score = post_data.get('score', 0)
        
        question = selftext if selftext else title
        
        # Filter relevance
        if len(question) < 20:
            print(f"   ⚠️  Question too short")
            return None
        
        singapore_keywords = ['singapore', 'bto', 'hdb', 'mop', 'resale', 'balloting', 'queue', 'cpf']
        if not any(keyword.lower() in question.lower() for keyword in singapore_keywords):
            print(f"   ⚠️  Not Singapore housing related")
            return None
        
        # Extract comments
        comments = []
        if len(data) > 1 and 'data' in data[1]:
            comment_data = data[1]['data']['children']
            
            for comment_item in comment_data:
                if comment_item['kind'] == 't1':
                    comment = comment_item['data']
                    comment_body = clean_text(comment.get('body', ''))
                    
                    if (comment_body and len(comment_body) > 30 and 
                        comment_body not in ['[deleted]', '[removed]']):
                        comments.append({
                            'text': comment_body,
                            'author': comment.get('author', 'unknown'),
                            'score': comment.get('score', 0),
                            'date': datetime.now().isoformat()
                        })
                        
                        if len(comments) >= 20:  # Limit comments
                            break
        
        result = {
            'url': url,
            'title': title,
            'question': question,
            'subreddit': subreddit,
            'author': author,
            'score': score,
            'comments': comments,
            'scraped_at': datetime.now().isoformat()
        }
        
        print(f"   ✅ Success: {len(comments)} comments")
        return result
        
    except Exception as e:
        print(f"   💥 Error: {e}")
        return None

def main():
    """Main function with manual URL list"""
    
    # Start with your original 7 URLs + some additional ones
    post_urls = [
        "https://www.reddit.com/r/singaporefi/comments/1aj7jdw/bto_advice/",
        "https://www.reddit.com/r/singaporefi/comments/1iwyrn8/advice_on_the_new_bto_rules_is_it_worth_it_vs/",
        "https://www.reddit.com/r/askSingapore/comments/1gg4lrl/whats_the_purpose_of_bto_when_it_is_always_over/",
        "https://www.reddit.com/r/singapore/comments/1ilx0uj/hdb_launches_5032_bto_flats_including_first/",
        "https://www.reddit.com/r/askSingapore/comments/1g3h2hr/parent_disagrees_with_my_bto_choice_opinions/",
        "https://www.reddit.com/r/askSingapore/comments/1ig2ihm/feeling_pressured_to_be_attached_because_of_bto/",
        "https://www.reddit.com/r/askSingapore/comments/1krqoqs/whats_your_bto_strategy_suggestions_ideas/",
        
        # Additional BTO/Housing URLs (add more as needed)
        "https://www.reddit.com/r/singapore/comments/1234567/bto_application_tips/",
        "https://www.reddit.com/r/askSingapore/comments/7890123/hdb_resale_vs_bto/",
        "https://www.reddit.com/r/singaporefi/comments/4567890/bto_financial_planning/"
    ]
    
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Rate-Limited Reddit Scraper                              ║
║                                                                              ║
║  🚦 Rate Limit: {REQUESTS_PER_MINUTE} requests/minute                                      ║
║  ⏱️  Delay: {MIN_DELAY}-{MAX_DELAY} seconds between requests                                ║
║  🎯 Target: {len(post_urls)} Singapore BTO/housing posts                                ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    all_results = []
    successful = 0
    
    for i, url in enumerate(post_urls, 1):
        print(f"\n📊 Processing {i}/{len(post_urls)}")
        
        result = scrape_single_post(url)
        if result:
            all_results.append(result)
            successful += 1
        
        print(f"   📈 Progress: {successful}/{i} successful")
    
    if not all_results:
        print("❌ No data collected.")
        return None
    
    # Convert to CSV format
    print(f"\n🔄 Converting {len(all_results)} posts to CSV format...")
    csv_data = []
    
    for post in all_results:
        question = post['question']
        comments = post.get('comments', [])
        
        if question:
            row = {'question': question}
            for i, comment in enumerate(comments, 1):
                row[f'answer{i}'] = comment['text']
            csv_data.append(row)
    
    # Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_rate_limited_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_rate_limited_{timestamp}.csv"
    if csv_data:
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value}"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    
    print(f"""
🎉 Rate-limited scraping completed!
📊 Statistics:
   • Posts processed: {len(post_urls)}
   • Posts successfully scraped: {successful}
   • Questions: {total_questions}
   • Answers: {total_answers}
   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}

📁 Files saved:
   • Raw data: {raw_file}
   • CSV format: {csv_file}

🚀 No rate limiting issues!
    """)
    
    return csv_file

if __name__ == "__main__":
    main()
