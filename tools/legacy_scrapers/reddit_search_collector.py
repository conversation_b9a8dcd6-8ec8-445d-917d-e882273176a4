#!/usr/bin/env python3
"""
Reddit Search Collector - Extract URLs from Reddit search results and scrape them
"""

import urllib.request
import urllib.parse
import json
import os
import ssl
import time
import random
import gzip
import re
from datetime import datetime

def create_ssl_context():
    """Create SSL context that handles certificate issues"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def get_random_user_agent():
    """Get random user agent"""
    user_agents = [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ]
    return random.choice(user_agents)

def safe_request(url, max_retries=3):
    """Make safe request with gzip handling and rate limiting"""
    
    for attempt in range(max_retries):
        try:
            # Long delay to avoid rate limiting
            delay = random.uniform(8, 12)  # 8-12 seconds (faster for more data)
            print(f"   ⏳ Waiting {delay:.1f}s before request...")
            time.sleep(delay)
            
            ssl_context = create_ssl_context()
            
            req = urllib.request.Request(
                url,
                headers={
                    'User-Agent': get_random_user_agent(),
                    'Accept': 'application/json, text/html, */*',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            )
            
            with urllib.request.urlopen(req, timeout=30, context=ssl_context) as response:
                # Handle gzip compression
                if response.info().get('Content-Encoding') == 'gzip':
                    data = gzip.decompress(response.read()).decode('utf-8')
                else:
                    data = response.read().decode('utf-8')
                
                # Try to parse as JSON first, fallback to HTML
                try:
                    return json.loads(data)
                except json.JSONDecodeError:
                    return data  # Return HTML for parsing
                
        except urllib.error.HTTPError as e:
            if e.code == 429:  # Rate limited
                wait_time = min(300, (4 ** attempt) * 30 + random.uniform(15, 30))
                print(f"   🚫 Rate limited (429). Waiting {wait_time:.1f}s...")
                time.sleep(wait_time)
            elif e.code == 403:
                print(f"   🚫 Access forbidden (403). Skipping...")
                return None
            elif e.code == 404:
                print(f"   ❌ Not found (404). Skipping...")
                return None
            else:
                print(f"   ❌ HTTP Error {e.code}")
                if attempt == max_retries - 1:
                    return None
                time.sleep(15 * (attempt + 1))
                
        except Exception as e:
            print(f"   💥 Request error: {e}")
            if attempt == max_retries - 1:
                return None
            time.sleep(15 * (attempt + 1))
    
    return None

def extract_post_urls_from_search_html(html_content):
    """Extract Reddit post URLs from search results HTML"""
    post_urls = set()
    
    try:
        # Pattern to match Reddit post URLs in HTML
        url_patterns = [
            r'href="(/r/[^/]+/comments/[^/]+/[^/"]+/)"',
            r'href="(https://www\.reddit\.com/r/[^/]+/comments/[^/]+/[^/"]+/)"',
            r'"permalink":"(/r/[^/]+/comments/[^/]+/[^/"]+/)"',
            r'"url":"(https://www\.reddit\.com/r/[^/]+/comments/[^/]+/[^/"]+/)"'
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if match.startswith('/r/'):
                    full_url = f"https://www.reddit.com{match}"
                else:
                    full_url = match
                
                # Filter for Singapore housing related subreddits
                if any(sub in full_url for sub in ['/r/singapore', '/r/askSingapore', '/r/singaporefi']):
                    post_urls.add(full_url.rstrip('/'))
        
        print(f"   📊 Extracted {len(post_urls)} unique post URLs from HTML")
        return list(post_urls)
        
    except Exception as e:
        print(f"   💥 Error extracting URLs from HTML: {e}")
        return []

def extract_post_urls_from_search_json(json_data):
    """Extract Reddit post URLs from search results JSON"""
    post_urls = set()
    
    try:
        if isinstance(json_data, dict) and 'data' in json_data:
            children = json_data['data'].get('children', [])
            
            for child in children:
                if child.get('kind') == 't3':  # Post type
                    post_data = child.get('data', {})
                    permalink = post_data.get('permalink', '')
                    subreddit = post_data.get('subreddit', '')
                    
                    if permalink and subreddit in ['singapore', 'askSingapore', 'singaporefi']:
                        full_url = f"https://www.reddit.com{permalink}"
                        post_urls.add(full_url.rstrip('/'))
        
        print(f"   📊 Extracted {len(post_urls)} unique post URLs from JSON")
        return list(post_urls)
        
    except Exception as e:
        print(f"   💥 Error extracting URLs from JSON: {e}")
        return []

def collect_search_urls():
    """Collect URLs from Reddit search results"""
    print("🔍 Collecting URLs from Reddit search results...")
    
    # Try different search approaches
    search_urls = [
        # JSON API searches
        "https://www.reddit.com/search.json?q=BTO+singapore&sort=relevance&limit=100",
        "https://www.reddit.com/search.json?q=singapore+BTO+advice&sort=relevance&limit=100",
        "https://www.reddit.com/search.json?q=HDB+singapore+BTO&sort=relevance&limit=100",
        
        # Subreddit specific searches
        "https://www.reddit.com/r/singapore/search.json?q=BTO&sort=relevance&limit=100&restrict_sr=1",
        "https://www.reddit.com/r/askSingapore/search.json?q=BTO&sort=relevance&limit=100&restrict_sr=1",
        "https://www.reddit.com/r/singaporefi/search.json?q=BTO&sort=relevance&limit=100&restrict_sr=1",
        
        # HTML search (your provided URL converted)
        "https://www.reddit.com/search/?q=bto+singapore"
    ]
    
    all_post_urls = set()
    
    for i, search_url in enumerate(search_urls, 1):
        print(f"\n🔄 Search {i}/{len(search_urls)}: {search_url}")
        
        try:
            result = safe_request(search_url)
            
            if result:
                if isinstance(result, dict):
                    # JSON response
                    urls = extract_post_urls_from_search_json(result)
                elif isinstance(result, str):
                    # HTML response
                    urls = extract_post_urls_from_search_html(result)
                else:
                    urls = []
                
                all_post_urls.update(urls)
                print(f"   ✅ Total unique URLs so far: {len(all_post_urls)}")
            else:
                print(f"   ❌ No data returned")
                
        except Exception as e:
            print(f"   💥 Error with search: {e}")
            continue
        
        # Extra delay between searches (reduced for faster collection)
        extra_delay = random.uniform(10, 15)
        print(f"   ⏸️  Extra delay: {extra_delay:.1f}s before next search...")
        time.sleep(extra_delay)
    
    final_urls = list(all_post_urls)
    print(f"\n🎯 Total unique post URLs collected: {len(final_urls)}")
    
    return final_urls

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    
    text = ' '.join(text.split())
    
    replacements = {
        '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
        '&#x27;': "'", '&#39;': "'", '&nbsp;': ' '
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text.strip()

def scrape_single_post(url):
    """Scrape a single Reddit post using JSON API"""
    try:
        print(f"🔄 Scraping: {url}")
        
        # Convert to JSON API
        json_url = url.rstrip('/') + '.json'
        
        # Make safe request
        data = safe_request(json_url)
        
        if not data or not isinstance(data, list) or len(data) < 1:
            print(f"   ❌ Invalid data structure")
            return None
        
        post_data = data[0]['data']['children'][0]['data']
        
        # Extract post info
        title = clean_text(post_data.get('title', ''))
        selftext = clean_text(post_data.get('selftext', ''))
        subreddit = post_data.get('subreddit', 'unknown')
        author = post_data.get('author', 'unknown')
        score = post_data.get('score', 0)
        
        question = selftext if selftext else title
        
        # Filter relevance
        if len(question) < 20:
            print(f"   ⚠️  Question too short")
            return None
        
        singapore_keywords = ['singapore', 'bto', 'hdb', 'mop', 'resale', 'balloting', 'queue', 'cpf', 'sg', 'sinkie']
        if not any(keyword.lower() in question.lower() for keyword in singapore_keywords):
            print(f"   ⚠️  Not Singapore housing related")
            return None
        
        # Extract comments
        comments = []
        if len(data) > 1 and 'data' in data[1]:
            comment_data = data[1]['data']['children']
            
            for comment_item in comment_data:
                if comment_item['kind'] == 't1':
                    comment = comment_item['data']
                    comment_body = clean_text(comment.get('body', ''))
                    
                    if (comment_body and len(comment_body) > 30 and 
                        comment_body not in ['[deleted]', '[removed]']):
                        comments.append({
                            'text': comment_body,
                            'author': comment.get('author', 'unknown'),
                            'score': comment.get('score', 0),
                            'date': datetime.now().isoformat()
                        })
                        
                        if len(comments) >= 25:
                            break
        
        result = {
            'url': url,
            'title': title,
            'question': question,
            'subreddit': subreddit,
            'author': author,
            'score': score,
            'comments': comments,
            'scraped_at': datetime.now().isoformat()
        }
        
        print(f"   ✅ Success: {len(comments)} comments")
        return result
        
    except Exception as e:
        print(f"   💥 Error: {e}")
        return None

def main():
    """Main function"""
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Reddit Search Collector                                  ║
║                                                                              ║
║  🔍 Step 1: Search Reddit for BTO Singapore posts                           ║
║  📊 Step 2: Extract individual post URLs                                    ║
║  🚀 Step 3: Scrape each post with JSON API                                  ║
║  ⚡ Change IP manually if you see rate limiting                             ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Step 1: Collect URLs from search results
    post_urls = collect_search_urls()
    
    if not post_urls:
        print("❌ No post URLs found. Exiting.")
        return None
    
    # Limit to reasonable number for this run - increase batch size
    max_posts = min(30, len(post_urls))  # Increased to 30 posts per run
    post_urls = post_urls[:max_posts]
    
    print(f"\n🎯 Selected {len(post_urls)} posts for scraping")
    
    # Step 2: Scrape each post
    all_results = []
    successful = 0
    
    for i, url in enumerate(post_urls, 1):
        print(f"\n📊 Processing {i}/{len(post_urls)}")
        
        result = scrape_single_post(url)
        if result:
            all_results.append(result)
            successful += 1
        
        print(f"   📈 Progress: {successful}/{i} successful")
        
        # Extra delay between posts (reduced for faster collection)
        if i < len(post_urls):
            extra_delay = random.uniform(8, 12)
            print(f"   ⏸️  Extra delay: {extra_delay:.1f}s before next post...")
            time.sleep(extra_delay)
    
    if not all_results:
        print("❌ No data collected.")
        return None
    
    # Step 3: Convert to CSV format
    print(f"\n🔄 Converting {len(all_results)} posts to CSV format...")
    csv_data = []
    
    for post in all_results:
        question = post['question']
        comments = post.get('comments', [])
        
        if question:
            row = {'question': question}
            for i, comment in enumerate(comments, 1):
                row[f'answer{i}'] = comment['text']
            csv_data.append(row)
    
    # Step 4: Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_search_collected_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_search_collected_{timestamp}.csv"
    if csv_data:
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value}"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    
    print(f"""
🎉 Reddit search collection completed!
📊 Statistics:
   • URLs found in search: {len(post_urls)}
   • Posts successfully scraped: {successful}
   • Questions: {total_questions}
   • Answers: {total_answers}
   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}

📁 Files saved:
   • Raw data: {raw_file}
   • CSV format: {csv_file}

🚀 Expanded Dataset for ML:
   • PropertyGuru: 2,400 questions
   • Reddit (previous batches): ~12 questions + ~241 answers  
   • Reddit (this search): {total_questions} questions + {total_answers} answers
   • Total: ~{2400 + 12 + total_questions} questions for Singapore BERT validation!

💡 To get even more data:
   1. Change your IP address
   2. Run this script again
   3. It will find different/more recent posts
   4. Gradually build up a massive dataset
    """)
    
    return csv_file

if __name__ == "__main__":
    main()
