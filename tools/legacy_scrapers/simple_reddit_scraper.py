#!/usr/bin/env python3
"""
Simple Reddit Scraper for Singapore Housing/BTO URLs
"""

import requests
import json
import os
from datetime import datetime
from bs4 import BeautifulSoup
import time

def scrape_reddit_url(url):
    """Scrape a single Reddit URL"""
    try:
        print(f"🔄 Scraping: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ HTTP {response.status_code} for {url}")
            return None
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract post title
        title_selectors = [
            'h1[slot="title"]',
            'h1',
            '[data-testid="post-content"] h1',
            '.title h1'
        ]
        
        title = "Unknown Title"
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text().strip()
                break
        
        # Extract post text
        post_text_selectors = [
            '[data-testid="post-content"] div[data-click-id="text"]',
            '.usertext-body .md',
            '[data-click-id="text"] p',
            '.Post div[data-click-id="text"]'
        ]
        
        post_text = ""
        for selector in post_text_selectors:
            text_elem = soup.select_one(selector)
            if text_elem:
                post_text = text_elem.get_text().strip()
                break
        
        # If no post text, use title as question
        if not post_text:
            post_text = title
        
        # Extract comments
        comments = []
        comment_selectors = [
            '[data-testid="comment"]',
            '.Comment',
            '.comment .usertext-body'
        ]
        
        for selector in comment_selectors:
            comment_elements = soup.select(selector)
            if comment_elements:
                for i, comment_elem in enumerate(comment_elements[:50]):  # Limit to 50
                    comment_text = comment_elem.get_text().strip()
                    if comment_text and len(comment_text) > 20:  # Filter short comments
                        comments.append({
                            'text': comment_text,
                            'author': f'User_{i+1}',
                            'date': datetime.now().isoformat()
                        })
                break
        
        # Extract subreddit
        subreddit = "unknown"
        if "/r/" in url:
            subreddit = url.split("/r/")[1].split("/")[0]
        
        result = {
            'url': url,
            'title': title,
            'question': post_text,
            'subreddit': subreddit,
            'comments': comments,
            'scraped_at': datetime.now().isoformat()
        }
        
        print(f"✅ Success: {len(comments)} comments found")
        return result
        
    except Exception as e:
        print(f"💥 Error scraping {url}: {e}")
        return None

def convert_to_csv_format(reddit_data):
    """Convert Reddit data to CSV format"""
    csv_rows = []
    
    for post in reddit_data:
        if not post or not post.get('comments'):
            continue
        
        question = post['question']
        comments = post['comments']
        
        # Create CSV row
        row = {'question': question}
        
        # Add answers (unlimited)
        for i, comment in enumerate(comments, 1):
            row[f'answer{i}'] = comment['text']
        
        csv_rows.append(row)
        print(f"   📊 Question with {len(comments)} answers")
    
    return csv_rows

def main():
    """Main scraping function"""
    urls = [
        "https://www.reddit.com/r/singaporefi/comments/1aj7jdw/bto_advice/",
        "https://www.reddit.com/r/singaporefi/comments/1iwyrn8/advice_on_the_new_bto_rules_is_it_worth_it_vs/",
        "https://www.reddit.com/r/askSingapore/comments/1gg4lrl/whats_the_purpose_of_bto_when_it_is_always_over/",
        "https://www.reddit.com/r/singapore/comments/1ilx0uj/hdb_launches_5032_bto_flats_including_first/",
        "https://www.reddit.com/r/askSingapore/comments/1g3h2hr/parent_disagrees_with_my_bto_choice_opinions/",
        "https://www.reddit.com/r/askSingapore/comments/1ig2ihm/feeling_pressured_to_be_attached_because_of_bto/",
        "https://www.reddit.com/r/askSingapore/comments/1krqoqs/whats_your_bto_strategy_suggestions_ideas/"
    ]
    
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Simple Reddit Scraper                                    ║
║                                                                              ║
║  🎯 Target: {len(urls)} Singapore housing/BTO Reddit posts                          ║
║  📊 Output: question, answer1, answer2, ... (unlimited)                     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    all_data = []
    successful = 0
    
    for i, url in enumerate(urls, 1):
        print(f"\n🔄 Processing {i}/{len(urls)}")
        
        result = scrape_reddit_url(url)
        if result:
            all_data.append(result)
            successful += 1
        
        # Polite delay
        time.sleep(3)
    
    print(f"\n📊 Scraping completed: {successful}/{len(urls)} successful")
    
    # Convert to CSV format
    print(f"\n🔄 Converting to CSV format...")
    csv_data = convert_to_csv_format(all_data)
    
    # Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_raw_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_csv_{timestamp}.csv"
    if csv_data:
        # Find all answer columns
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Headers
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            # Data
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value}"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    
    print(f"\n🎉 Reddit scraping completed!")
    print(f"📊 Statistics:")
    print(f"   • Questions: {total_questions}")
    print(f"   • Answers: {total_answers}")
    print(f"   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}")
    
    print(f"\n📁 Files saved:")
    print(f"   • Raw data: {raw_file}")
    print(f"   • CSV format: {csv_file}")
    
    return csv_file

if __name__ == "__main__":
    main()
