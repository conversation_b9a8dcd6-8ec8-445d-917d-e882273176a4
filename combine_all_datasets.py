#!/usr/bin/env python3
"""
Combine All Datasets - Merge PropertyGuru + Reddit data for ML validation
"""

import json
import csv
import os
import pandas as pd
from datetime import datetime
import glob

def load_propertyguru_data():
    """Load PropertyGuru data from the final combined file"""
    print("🏠 Loading PropertyGuru data...")
    
    # Look for the most recent PropertyGuru combined file
    pg_files = glob.glob("data/final/propertyguru_ALL_COMBINED_*.json")
    if not pg_files:
        print("   ❌ No PropertyGuru combined file found")
        return []
    
    # Get the most recent file
    latest_pg_file = max(pg_files, key=os.path.getctime)
    print(f"   📁 Found: {latest_pg_file}")
    
    try:
        with open(latest_pg_file, 'r', encoding='utf-8') as f:
            pg_data = json.load(f)
        
        print(f"   ✅ Loaded {len(pg_data)} PropertyGuru entries")
        return pg_data
    except Exception as e:
        print(f"   ❌ Error loading PropertyGuru data: {e}")
        return []

def load_reddit_data():
    """Load all Reddit data files"""
    print("🔍 Loading Reddit data...")
    
    all_reddit_data = []
    
    # Find all Reddit JSON files
    reddit_files = glob.glob("data/processed/reddit_*.json")
    
    if not reddit_files:
        print("   ❌ No Reddit files found")
        return []
    
    print(f"   📁 Found {len(reddit_files)} Reddit files")
    
    for file_path in reddit_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                all_reddit_data.extend(data)
                print(f"   ✅ Loaded {len(data)} entries from {os.path.basename(file_path)}")
            else:
                print(f"   ⚠️  Unexpected format in {os.path.basename(file_path)}")
                
        except Exception as e:
            print(f"   ❌ Error loading {os.path.basename(file_path)}: {e}")
    
    print(f"   🎯 Total Reddit entries: {len(all_reddit_data)}")
    return all_reddit_data

def convert_propertyguru_to_unified_format(pg_data):
    """Convert PropertyGuru data to unified format"""
    print("🔄 Converting PropertyGuru data to unified format...")
    
    unified_data = []
    
    for entry in pg_data:
        try:
            question = entry.get('question', '').strip()
            answers = entry.get('answers', [])
            
            if not question or not answers:
                continue
            
            # Create unified entry
            unified_entry = {
                'source': 'PropertyGuru',
                'question': question,
                'answers': [ans.get('answer', '').strip() for ans in answers if ans.get('answer', '').strip()],
                'metadata': {
                    'url': entry.get('url', ''),
                    'title': entry.get('title', ''),
                    'scraped_at': entry.get('scraped_at', ''),
                    'answer_count': len(answers)
                }
            }
            
            if unified_entry['answers']:  # Only add if has valid answers
                unified_data.append(unified_entry)
                
        except Exception as e:
            print(f"   ⚠️  Error processing PropertyGuru entry: {e}")
            continue
    
    print(f"   ✅ Converted {len(unified_data)} PropertyGuru entries")
    return unified_data

def clean_text(text):
    """Clean and filter text content"""
    if not text:
        return ""

    # Remove excessive whitespace
    text = ' '.join(text.split())

    # Filter out very short responses
    if len(text) < 15:
        return ""

    # Filter out common bot/spam patterns
    spam_patterns = [
        "This post was mass deleted",
        "[deleted]",
        "[removed]",
        "I am a bot",
        "PM SG_wormsbot",
        "Read More",
        "WhatsApp me at:",
        "Contact me at:",
        "Call me at:",
        "Email me at:"
    ]

    for pattern in spam_patterns:
        if pattern.lower() in text.lower():
            return ""

    # Filter out very long promotional content (likely spam)
    if len(text) > 2000 and any(word in text.lower() for word in ['agent', 'property', 'contact', 'whatsapp', 'email']):
        return ""

    return text

def convert_reddit_to_unified_format(reddit_data):
    """Convert Reddit data to unified format"""
    print("🔄 Converting Reddit data to unified format...")

    unified_data = []

    for entry in reddit_data:
        try:
            question = entry.get('question', '').strip()
            comments = entry.get('comments', [])

            if not question or not comments:
                continue

            # Extract and clean answer texts from comments
            answers = []
            for comment in comments:
                if isinstance(comment, dict):
                    answer_text = comment.get('text', '').strip()
                elif isinstance(comment, str):
                    answer_text = comment.strip()
                else:
                    continue

                # Clean the text
                cleaned_text = clean_text(answer_text)
                if cleaned_text:
                    answers.append(cleaned_text)

            if not answers:
                continue

            # Clean the question too
            cleaned_question = clean_text(question)
            if not cleaned_question:
                continue

            # Create unified entry
            unified_entry = {
                'source': 'Reddit',
                'question': cleaned_question,
                'answers': answers,
                'metadata': {
                    'url': entry.get('url', ''),
                    'title': entry.get('title', ''),
                    'subreddit': entry.get('subreddit', ''),
                    'author': entry.get('author', ''),
                    'score': entry.get('score', 0),
                    'scraped_at': entry.get('scraped_at', ''),
                    'answer_count': len(answers)
                }
            }

            unified_data.append(unified_entry)

        except Exception as e:
            print(f"   ⚠️  Error processing Reddit entry: {e}")
            continue

    print(f"   ✅ Converted {len(unified_data)} Reddit entries")
    return unified_data

def create_ml_ready_datasets(unified_data):
    """Create ML-ready datasets in different formats"""
    print("🤖 Creating ML-ready datasets...")

    # Format 1: Question-Answer pairs (for BERT fine-tuning)
    qa_pairs = []
    for entry in unified_data:
        question = entry['question']
        for answer in entry['answers']:
            qa_pairs.append({
                'question': question,
                'answer': answer,
                'source': entry['source'],
                'url': entry['metadata'].get('url', ''),
                'subreddit': entry['metadata'].get('subreddit', '') if entry['source'] == 'Reddit' else ''
            })

    # Format 2: Multi-answer format (USER REQUESTED SCHEMA: question, answer_1, answer_2, ..., answer_x)
    multi_answer = []
    max_answers = 0

    # First pass: find maximum number of answers to determine column count
    for entry in unified_data:
        if len(entry['answers']) > max_answers:
            max_answers = len(entry['answers'])

    print(f"   📊 Maximum answers per question: {max_answers}")

    # Second pass: create the multi-answer format with all entries
    for entry in unified_data:
        multi_entry = {
            'question': entry['question'],
            'source': entry['source'],
            'answer_count': len(entry['answers']),
            'url': entry['metadata'].get('url', ''),
            'subreddit': entry['metadata'].get('subreddit', '') if entry['source'] == 'Reddit' else '',
            'title': entry['metadata'].get('title', ''),
            'author': entry['metadata'].get('author', '') if entry['source'] == 'Reddit' else '',
            'score': entry['metadata'].get('score', 0) if entry['source'] == 'Reddit' else 0
        }

        # Add individual answers as separate columns (answer_1, answer_2, etc.)
        for i in range(1, max_answers + 1):
            if i <= len(entry['answers']):
                multi_entry[f'answer_{i}'] = entry['answers'][i-1]
            else:
                multi_entry[f'answer_{i}'] = ''  # Empty string for missing answers

        multi_answer.append(multi_entry)

    # Format 3: Text classification format (source prediction)
    text_classification = []
    for entry in unified_data:
        for answer in entry['answers']:
            text_classification.append({
                'text': answer,
                'label': entry['source'],
                'question': entry['question']
            })

    print(f"   ✅ Created {len(qa_pairs)} question-answer pairs")
    print(f"   ✅ Created {len(multi_answer)} multi-answer entries (with {max_answers} answer columns)")
    print(f"   ✅ Created {len(text_classification)} text classification entries")

    return qa_pairs, multi_answer, text_classification, max_answers

def save_datasets(unified_data, qa_pairs, multi_answer, text_classification, max_answers):
    """Save all datasets in multiple formats"""
    print("💾 Saving combined datasets...")

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = "data/combined"
    os.makedirs(output_dir, exist_ok=True)

    # Save unified raw data (JSON)
    unified_file = f"{output_dir}/singapore_housing_unified_{timestamp}.json"
    with open(unified_file, 'w', encoding='utf-8') as f:
        json.dump(unified_data, f, indent=2, ensure_ascii=False)

    # Save QA pairs (CSV and JSON)
    qa_csv_file = f"{output_dir}/singapore_housing_qa_pairs_{timestamp}.csv"
    qa_json_file = f"{output_dir}/singapore_housing_qa_pairs_{timestamp}.json"

    pd.DataFrame(qa_pairs).to_csv(qa_csv_file, index=False, encoding='utf-8')
    with open(qa_json_file, 'w', encoding='utf-8') as f:
        json.dump(qa_pairs, f, indent=2, ensure_ascii=False)

    # Save multi-answer format (CSV and JSON) - USER REQUESTED SCHEMA
    ma_csv_file = f"{output_dir}/singapore_housing_COMBINED_SCHEMA_{timestamp}.csv"
    ma_json_file = f"{output_dir}/singapore_housing_COMBINED_SCHEMA_{timestamp}.json"

    # Create DataFrame with proper column ordering
    df_multi = pd.DataFrame(multi_answer)

    # Reorder columns to have question first, then all answer columns, then metadata
    base_columns = ['question', 'source', 'answer_count']
    answer_columns = [f'answer_{i}' for i in range(1, max_answers + 1)]
    metadata_columns = ['url', 'subreddit', 'title', 'author', 'score']

    # Ensure all columns exist and reorder
    column_order = base_columns + answer_columns + metadata_columns
    existing_columns = [col for col in column_order if col in df_multi.columns]
    df_multi = df_multi[existing_columns]

    df_multi.to_csv(ma_csv_file, index=False, encoding='utf-8')
    with open(ma_json_file, 'w', encoding='utf-8') as f:
        json.dump(multi_answer, f, indent=2, ensure_ascii=False)

    # Save text classification format (CSV and JSON)
    tc_csv_file = f"{output_dir}/singapore_housing_text_classification_{timestamp}.csv"
    tc_json_file = f"{output_dir}/singapore_housing_text_classification_{timestamp}.json"

    pd.DataFrame(text_classification).to_csv(tc_csv_file, index=False, encoding='utf-8')
    with open(tc_json_file, 'w', encoding='utf-8') as f:
        json.dump(text_classification, f, indent=2, ensure_ascii=False)

    return {
        'unified': unified_file,
        'qa_pairs_csv': qa_csv_file,
        'qa_pairs_json': qa_json_file,
        'combined_schema_csv': ma_csv_file,  # This is your requested format
        'combined_schema_json': ma_json_file,
        'text_classification_csv': tc_csv_file,
        'text_classification_json': tc_json_file
    }

def generate_dataset_statistics(unified_data, qa_pairs, multi_answer, text_classification):
    """Generate comprehensive dataset statistics"""
    print("📊 Generating dataset statistics...")
    
    # Source distribution
    source_counts = {}
    total_questions = len(unified_data)
    total_answers = sum(len(entry['answers']) for entry in unified_data)
    
    for entry in unified_data:
        source = entry['source']
        source_counts[source] = source_counts.get(source, 0) + 1
    
    # Answer length statistics
    answer_lengths = []
    for entry in unified_data:
        for answer in entry['answers']:
            answer_lengths.append(len(answer.split()))
    
    # Question length statistics
    question_lengths = [len(entry['question'].split()) for entry in unified_data]
    
    stats = {
        'total_questions': total_questions,
        'total_answers': total_answers,
        'total_qa_pairs': len(qa_pairs),
        'total_multi_answer_entries': len(multi_answer),
        'total_text_classification_entries': len(text_classification),
        'source_distribution': source_counts,
        'avg_answers_per_question': total_answers / total_questions if total_questions > 0 else 0,
        'avg_answer_length_words': sum(answer_lengths) / len(answer_lengths) if answer_lengths else 0,
        'avg_question_length_words': sum(question_lengths) / len(question_lengths) if question_lengths else 0,
        'max_answer_length_words': max(answer_lengths) if answer_lengths else 0,
        'max_question_length_words': max(question_lengths) if question_lengths else 0
    }
    
    return stats

def main():
    """Main function to combine all datasets"""
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Singapore Housing Dataset Combiner                       ║
║                                                                              ║
║  🏠 PropertyGuru: Expert Q&A data                                           ║
║  🔍 Reddit: Community discussions                                           ║
║  🤖 Output: ML-ready datasets for Vertex AI                                 ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Step 1: Load all data
    pg_data = load_propertyguru_data()
    reddit_data = load_reddit_data()
    
    if not pg_data and not reddit_data:
        print("❌ No data found to combine!")
        return None
    
    # Step 2: Convert to unified format
    unified_pg = convert_propertyguru_to_unified_format(pg_data)
    unified_reddit = convert_reddit_to_unified_format(reddit_data)
    
    # Step 3: Combine all data
    unified_data = unified_pg + unified_reddit
    print(f"\n🎯 Total unified entries: {len(unified_data)}")
    
    # Step 4: Create ML-ready datasets
    qa_pairs, multi_answer, text_classification, max_answers = create_ml_ready_datasets(unified_data)

    # Step 5: Save datasets
    saved_files = save_datasets(unified_data, qa_pairs, multi_answer, text_classification, max_answers)
    
    # Step 6: Generate statistics
    stats = generate_dataset_statistics(unified_data, qa_pairs, multi_answer, text_classification)
    
    # Save statistics
    stats_file = f"data/combined/dataset_statistics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    # Print final summary
    print(f"""
🎉 Dataset combination completed successfully!

📊 Final Statistics:
   • Total Questions: {stats['total_questions']:,}
   • Total Answers: {stats['total_answers']:,}
   • Total QA Pairs: {stats['total_qa_pairs']:,}
   • PropertyGuru Entries: {stats['source_distribution'].get('PropertyGuru', 0):,}
   • Reddit Entries: {stats['source_distribution'].get('Reddit', 0):,}
   • Avg Answers per Question: {stats['avg_answers_per_question']:.1f}
   • Avg Answer Length: {stats['avg_answer_length_words']:.1f} words
   • Avg Question Length: {stats['avg_question_length_words']:.1f} words

📁 Files Created:
   • Unified Dataset: {os.path.basename(saved_files['unified'])}
   • QA Pairs CSV: {os.path.basename(saved_files['qa_pairs_csv'])}
   • QA Pairs JSON: {os.path.basename(saved_files['qa_pairs_json'])}
   • 🎯 COMBINED SCHEMA CSV: {os.path.basename(saved_files['combined_schema_csv'])} (YOUR REQUESTED FORMAT!)
   • 🎯 COMBINED SCHEMA JSON: {os.path.basename(saved_files['combined_schema_json'])}
   • Text Classification CSV: {os.path.basename(saved_files['text_classification_csv'])}
   • Text Classification JSON: {os.path.basename(saved_files['text_classification_json'])}
   • Statistics: {os.path.basename(stats_file)}

🚀 Ready for Vertex AI on Google Cloud!
   • Upload the CSV/JSON files to Google Cloud Storage
   • Use QA pairs for BERT fine-tuning
   • Use text classification for source prediction
   • Use multi-answer for answer ranking tasks

🇸🇬 Perfect for Singapore BERT vs Baseline BERT comparison!
    """)
    
    return saved_files, stats

if __name__ == "__main__":
    main()
