# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
config/*.json
*.log
temp/
tmp/

# Data files (keep structure but ignore large files)
data/combined/*.csv
data/combined/*.json
data/final/*.csv
data/processed/*.csv
data/archive/*.csv

# ML outputs
ml_validation/results/*.pkl
ml_validation/results/*.model
ml_validation/notebooks/.ipynb_checkpoints/
