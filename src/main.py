import asyncio
import json
import logging
from typing import List, Dict, Any
from .property_guru_scraper import scrape_property_guru, scrape_property_guru_comprehensive
from .hardware_zone_scraper import scrape_hardware_zone
from .reddit_scraper import scrape_reddit
from .gemini_processor import GeminiProcessor
import os

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BTODataScraper:
    def __init__(self, gemini_api_key: str = None, comprehensive_mode: bool = False):
        """
        Initialize the BTO data scraper

        Args:
            gemini_api_key: Gemini API key. If None, will try to get from environment
            comprehensive_mode: If True, scrape all PropertyGuru pages (708 pages)
        """
        if gemini_api_key is None:
            gemini_api_key = os.getenv('GEMINI_API_KEY')

        if not gemini_api_key:
            logger.warning("GEMINI_API_KEY environment variable not found. Gemini processing will be disabled.")

        self.gemini_processor = GeminiProcessor(gemini_api_key)
        self.comprehensive_mode = comprehensive_mode

    async def run_scraping_pipeline(self) -> List[Dict[str, Any]]:
        """
        Run the complete scraping pipeline

        Returns:
            List of processed data dictionaries
        """
        logger.info("Starting BTO data scraping pipeline")

        all_data = []

        try:
            # Step 1: Scrape PropertyGuru
            logger.info("=== Scraping PropertyGuru ===")
            if self.comprehensive_mode:
                logger.info("Running in COMPREHENSIVE mode - scraping all 708 pages")
                property_guru_data = await scrape_property_guru_comprehensive()
            else:
                logger.info("Running in DEMO mode - scraping 5 questions")
                property_guru_data = await scrape_property_guru(max_questions=5)

            logger.info(f"PropertyGuru: {len(property_guru_data)} items scraped")
            all_data.extend(property_guru_data)

            # Step 2: Scrape Reddit
            logger.info("=== Scraping Reddit ===")
            reddit_data = await scrape_reddit()
            logger.info(f"Reddit: {len(reddit_data)} items scraped")
            all_data.extend(reddit_data)

            # Step 3: Scrape HardwareZone
            logger.info("=== Scraping HardwareZone ===")
            hwz_url = "https://forums.hardwarezone.com.sg/threads/bto-inflation-quite-hiong.7092360/"
            hardware_zone_data = await scrape_hardware_zone(url=hwz_url)
            logger.info(f"HardwareZone: {len(hardware_zone_data)} items scraped")
            all_data.extend(hardware_zone_data)

            # Step 4: Process with Gemini
            if self.gemini_processor.enabled:
                logger.info("=== Processing with Gemini ===")
                processed_data = await self.process_all_text(all_data)
                logger.info(f"Pipeline completed. Total processed items: {len(processed_data)}")
                return processed_data
            else:
                logger.warning("Skipping Gemini processing as it is disabled.")
                return all_data

        except Exception as e:
            logger.error(f"Error in scraping pipeline: {e}", exc_info=True)
            return []

    async def process_all_text(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process the text of posts and their comments/answers using Gemini.
        """
        for post in data:
            # Process the main post text
            if post.get('text'):
                # Use a list to call the processor
                processed_post_list = await self.gemini_processor.process_with_gemini([{'text': post['text']}])
                if processed_post_list and 'processed_text' in processed_post_list[0]:
                    post['processed_text'] = processed_post_list[0]['processed_text']

            # Process comments or answers
            children_key = 'comments' if 'comments' in post else 'answers'
            if children_key in post and post[children_key]:
                children = post[children_key]
                # Create a list of dicts for batch processing
                children_to_process = [{'text': child.get('text', '')} for child in children if child.get('text')]

                if children_to_process:
                    processed_children = await self.gemini_processor.batch_process(children_to_process)

                    # Create a map from original text to processed text for easy lookup
                    processed_text_map = {item.get('original_text'): item.get('processed_text') for item in processed_children}

                    # Assign processed text back to the children
                    for child in children:
                        original_text = child.get('text', '')
                        if original_text in processed_text_map:
                            child['processed_text'] = processed_text_map[original_text]
                        else:
                            child['processed_text'] = original_text # Fallback to original if not processed
        return data

    async def save_to_json(self, data: List[Dict[str, Any]], filename: str = "bto_processed_reviews.json"):
        """
        Save processed data to JSON file

        Args:
            data: Processed data to save
            filename: Output filename
        """
        # Get the absolute path to the project root
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        output_path = os.path.join(project_root, filename)

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            logger.info(f"Data saved to {output_path}")

        except Exception as e:
            logger.error(f"Error saving data to {output_path}: {e}")

    async def run_with_save(self, output_filename: str = "bto_processed_reviews.json"):
        """
        Run the complete pipeline and save results

        Args:
            output_filename: Name of the output JSON file
        """
        processed_data = await self.run_scraping_pipeline()

        if processed_data:
            await self.save_to_json(processed_data, output_filename)
            logger.info(f"Pipeline completed successfully. Output saved to {output_filename}")
        else:
            logger.warning("No data was processed. Check the logs for errors.")

async def main():
    """Main function to run the scraper"""
    import argparse

    parser = argparse.ArgumentParser(description='BTO Data Scraper')
    parser.add_argument('--comprehensive', action='store_true',
                       help='Run comprehensive PropertyGuru scraping (all 708 pages)')

    args = parser.parse_args()

    # Initialize scraper
    scraper = BTODataScraper(comprehensive_mode=args.comprehensive)

    # Run the pipeline
    if args.comprehensive:
        output_filename = f"bto_comprehensive_reviews_{asyncio.get_event_loop().time():.0f}.json"
    else:
        output_filename = "bto_processed_reviews.json"

    await scraper.run_with_save(output_filename)

if __name__ == "__main__":
    # To run this script from the project root, use:
    # python -m mvp_scraper.main
    # python -m mvp_scraper.main --comprehensive
    asyncio.run(main())