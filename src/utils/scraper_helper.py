#!/usr/bin/env python3
"""
Scraper Helper - Simple web scraping utilities
"""

import asyncio
from typing import Optional

class ScraperHelper:
    def __init__(self):
        self.session = None

    async def scrape_with_fallback(self, url: str) -> Optional[str]:
        """Scrape URL with fallback methods"""
        try:
            import requests

            # Try requests first (simpler)
            response = requests.get(url, headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            }, timeout=30)

            if response.status_code == 200:
                return response.text
            else:
                print(f"⚠️  HTTP {response.status_code} for {url}")
                return None

        except Exception as e:
            print(f"💥 Error scraping {url}: {e}")
            return None