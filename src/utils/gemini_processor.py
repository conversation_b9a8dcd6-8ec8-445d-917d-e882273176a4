import asyncio
import google.generativeai as genai
from typing import List, Dict, Any
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiProcessor:
    def __init__(self, api_key: str = None):
        """
        Initialize Gemini processor
        
        Args:
            api_key: Gemini API key. If None, will try to get from environment
        """
        if api_key is None:
            api_key = os.getenv('GEMINI_API_KEY')
            
        if not api_key:
            logger.warning("Gemini API key not found. GeminiProcessor will be disabled.")
            self.enabled = False
            self.model = None
            return
        
        self.enabled = True
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        
    async def process_with_gemini(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process raw scraped data with Gemini API
        
        Args:
            raw_data: List of dictionaries containing scraped data
            
        Returns:
            List of dictionaries with processed text added
        """
        processed_data = []
        
        for i, item in enumerate(raw_data):
            try:
                logger.info(f"Processing item {i+1}/{len(raw_data)}")
                
                # Extract raw text
                raw_text = item.get('text', '')
                if not raw_text.strip():
                    logger.warning(f"Empty text for item {i+1}, skipping")
                    continue
                
                # Process with Gemini
                cleaned_text = await self._call_gemini_api(raw_text)
                
                # Create processed item
                processed_item = {
                    **item,
                    'processed_text': cleaned_text,
                    'original_text': raw_text  # Keep original for reference
                }
                
                processed_data.append(processed_item)
                
                # Add delay to be respectful to API
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error processing item {i+1}: {e}")
                # Add item with original text if processing fails
                processed_item = {
                    **item,
                    'processed_text': item.get('text', ''),
                    'original_text': item.get('text', ''),
                    'processing_error': str(e)
                }
                processed_data.append(processed_item)
        
        return processed_data
    
    async def _call_gemini_api(self, raw_text: str) -> str:
        """
        Call Gemini API to clean and format text
        
        Args:
            raw_text: Raw text to process
            
        Returns:
            Cleaned and formatted text
        """
        try:
            prompt = f"""
            Please clean and format the following text for sentiment analysis:
            
            {raw_text}
            
            Instructions:
            1. Remove any HTML tags, formatting, or non-textual artifacts
            2. Normalize whitespace (remove excessive spaces, newlines, tabs)
            3. Translate to English if the text is in another language
            4. Format as a single coherent paragraph
            5. Remove any irrelevant content like navigation elements, ads, or signatures
            6. Keep the core meaning and sentiment intact
            
            Return only the cleaned text, no explanations.
            """
            
            response = self.model.generate_content(prompt)
            
            if response.text:
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini API")
                return raw_text
                
        except Exception as e:
            logger.error(f"Error calling Gemini API: {e}")
            return raw_text  # Return original text if API fails
    
    async def batch_process(self, raw_data: List[Dict[str, Any]], 
                           batch_size: int = 5) -> List[Dict[str, Any]]:
        """
        Process data in batches to manage API rate limits
        
        Args:
            raw_data: List of raw data to process
            batch_size: Number of items to process in each batch
            
        Returns:
            List of processed data
        """
        if not self.enabled:
            logger.warning("GeminiProcessor is disabled. Returning raw data.")
            return raw_data

        processed_data = []
        
        for i in range(0, len(raw_data), batch_size):
            batch = raw_data[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(raw_data) + batch_size - 1)//batch_size}")
            
            batch_processed = await self.process_with_gemini(batch)
            processed_data.extend(batch_processed)
            
            # Add delay between batches
            if i + batch_size < len(raw_data):
                await asyncio.sleep(2)
        
        return processed_data 