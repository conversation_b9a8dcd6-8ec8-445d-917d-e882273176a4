#!/usr/bin/env python3
"""
Reddit Scraper for Singapore Housing/BTO Discussions
Scrapes specific Reddit URLs and formats as question, answer1, answer2, etc.
"""

import asyncio
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from .scraper_helper import ScraperHelper

class RedditScraper:
    def __init__(self):
        self.scraper_helper = ScraperHelper()
        self.target_urls = [
            "https://www.reddit.com/r/singaporefi/comments/1aj7jdw/bto_advice/",
            "https://www.reddit.com/r/singaporefi/comments/1iwyrn8/advice_on_the_new_bto_rules_is_it_worth_it_vs/",
            "https://www.reddit.com/r/askSingapore/comments/1gg4lrl/whats_the_purpose_of_bto_when_it_is_always_over/",
            "https://www.reddit.com/r/singapore/comments/1ilx0uj/hdb_launches_5032_bto_flats_including_first/",
            "https://www.reddit.com/r/askSingapore/comments/1g3h2hr/parent_disagrees_with_my_bto_choice_opinions/",
            "https://www.reddit.com/r/askSingapore/comments/1ig2ihm/feeling_pressured_to_be_attached_because_of_bto/",
            "https://www.reddit.com/r/askSingapore/comments/1krqoqs/whats_your_bto_strategy_suggestions_ideas/"
        ]

    async def scrape_reddit_post(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape a single Reddit post and its comments"""
        try:
            print(f"🔄 Scraping Reddit post: {url}")

            # Use scraper helper to get page content
            content = await self.scraper_helper.scrape_with_fallback(url)

            if not content:
                print(f"❌ Failed to scrape {url}")
                return None

            # Parse Reddit post and comments
            post_data = await self.parse_reddit_content(content, url)

            if post_data:
                print(f"✅ Successfully scraped: {post_data.get('title', 'Unknown')}")
                print(f"   📊 Found {len(post_data.get('comments', []))} comments")
                return post_data
            else:
                print(f"⚠️  No data extracted from {url}")
                return None

        except Exception as e:
            print(f"💥 Error scraping {url}: {e}")
            return None

    async def parse_reddit_content(self, content: str, url: str) -> Optional[Dict[str, Any]]:
        """Parse Reddit HTML content to extract post and comments"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')

            # Extract post title and text
            title_element = soup.find('h1') or soup.find('[data-testid="post-content"]') or soup.find('.title')
            title = title_element.get_text().strip() if title_element else "Unknown Title"

            # Extract post text content
            post_text_selectors = [
                '[data-testid="post-content"] p',
                '.usertext-body p',
                '.md p',
                'div[data-click-id="text"] p'
            ]

            post_text = ""
            for selector in post_text_selectors:
                elements = soup.select(selector)
                if elements:
                    post_text = " ".join([elem.get_text().strip() for elem in elements])
                    break

            # If no post text found, use title as question
            if not post_text:
                post_text = title

            # Extract comments
            comments = []
            comment_selectors = [
                '[data-testid="comment"]',
                '.comment .usertext-body',
                '.Comment',
                'div[data-click-id="comment"]'
            ]

            for selector in comment_selectors:
                comment_elements = soup.select(selector)
                if comment_elements:
                    for i, comment_elem in enumerate(comment_elements[:50]):  # Limit to 50 comments
                        comment_text = comment_elem.get_text().strip()
                        if comment_text and len(comment_text) > 10:  # Filter out very short comments
                            # Extract author if possible
                            author_elem = comment_elem.find('a[href*="/user/"]') or comment_elem.find('.author')
                            author = author_elem.get_text().strip() if author_elem else f"User_{i+1}"

                            comments.append({
                                "comment_id": f"c{i+1}",
                                "text": comment_text,
                                "author": author,
                                "date": datetime.now().isoformat(),
                                "upvotes": "",
                                "post_url": url
                            })
                    break

            # Extract subreddit from URL
            subreddit = "unknown"
            if "/r/" in url:
                subreddit = url.split("/r/")[1].split("/")[0]

            return {
                "source": "reddit",
                "type": "post",
                "text": post_text,
                "author": "reddit_user",
                "date": datetime.now().isoformat(),
                "url": url,
                "title": title,
                "subreddit": subreddit,
                "upvotes": "",
                "comment_count": str(len(comments)),
                "raw_data": {
                    "text": post_text,
                    "author": "reddit_user",
                    "date": datetime.now().isoformat(),
                    "subreddit": subreddit,
                    "upvotes": "",
                    "url": url,
                    "source": "selenium"
                },
                "comments": comments
            }

        except Exception as e:
            print(f"💥 Error parsing Reddit content: {e}")
            return None

    def convert_to_qa_format(self, reddit_posts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert Reddit posts to question-answer format"""
        qa_data = []

        for post in reddit_posts:
            if not post:
                continue

            # Use post text/title as question
            question_text = post.get('text', '') or post.get('title', '')
            if not question_text:
                continue

            # Get comments as answers
            comments = post.get('comments', [])
            if not comments:
                continue

            # Create answer list (no limit, as many as possible)
            answers = []
            for comment in comments:
                comment_text = comment.get('text', '').strip()
                if comment_text and len(comment_text) > 10:  # Filter very short comments
                    answers.append({
                        'text': comment_text,
                        'author': comment.get('author', 'Anonymous'),
                        'date': comment.get('date', ''),
                        'upvotes': comment.get('upvotes', '')
                    })

            if answers:  # Only include if there are answers
                qa_record = {
                    'text': question_text,
                    'url': post.get('url', ''),
                    'author': post.get('author', 'Anonymous'),
                    'date': post.get('date', ''),
                    'source': 'reddit',
                    'subreddit': post.get('subreddit', ''),
                    'title': post.get('title', ''),
                    'answers': answers
                }

                qa_data.append(qa_record)
                print(f"   ✅ Converted: {len(answers)} answers for question")

        return qa_data

    def convert_to_csv_format(self, qa_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert Q&A data to CSV format: question, answer1, answer2, ..."""
        csv_data = []

        for qa in qa_data:
            question = qa.get('text', '').strip()
            answers = qa.get('answers', [])

            if not question or not answers:
                continue

            # Create CSV row
            csv_row = {'question': question}

            # Add answers (no limit - as many as possible)
            for i, answer in enumerate(answers, 1):
                csv_row[f'answer{i}'] = answer.get('text', '').strip()

            csv_data.append(csv_row)
            print(f"   📊 CSV row: 1 question + {len(answers)} answers")

        return csv_data

    async def scrape_all_reddit_urls(self) -> Dict[str, Any]:
        """Scrape all target Reddit URLs"""
        print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Reddit Singapore Housing Scraper                         ║
║                                                                              ║
║  🎯 Target: {len(self.target_urls)} Reddit posts about Singapore housing/BTO                ║
║  📊 Format: question, answer1, answer2, answer3, ... (unlimited)            ║
║  🇸🇬 Focus: Singapore housing discussions and advice                        ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)

        all_posts = []
        successful_scrapes = 0

        for i, url in enumerate(self.target_urls, 1):
            print(f"\n🔄 Processing {i}/{len(self.target_urls)}: {url}")

            post_data = await self.scrape_reddit_post(url)
            if post_data:
                all_posts.append(post_data)
                successful_scrapes += 1

            # Polite delay between requests
            await asyncio.sleep(2)

        print(f"\n📊 Scraping Summary:")
        print(f"   • URLs processed: {len(self.target_urls)}")
        print(f"   • Successful scrapes: {successful_scrapes}")
        print(f"   • Failed scrapes: {len(self.target_urls) - successful_scrapes}")

        # Convert to Q&A format
        print(f"\n🔄 Converting to Q&A format...")
        qa_data = self.convert_to_qa_format(all_posts)

        # Convert to CSV format
        print(f"\n🔄 Converting to CSV format...")
        csv_data = self.convert_to_csv_format(qa_data)

        # Calculate statistics
        total_questions = len(csv_data)
        total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
        max_answers = max([len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data]) if csv_data else 0

        print(f"\n🎉 Reddit Scraping Completed!")
        print(f"📊 Final Statistics:")
        print(f"   • Total questions: {total_questions}")
        print(f"   • Total answers: {total_answers}")
        print(f"   • Average answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}")
        print(f"   • Maximum answers for one question: {max_answers}")

        return {
            'raw_posts': all_posts,
            'qa_format': qa_data,
            'csv_format': csv_data,
            'statistics': {
                'total_questions': total_questions,
                'total_answers': total_answers,
                'max_answers_per_question': max_answers,
                'successful_scrapes': successful_scrapes,
                'failed_scrapes': len(self.target_urls) - successful_scrapes
            }
        }

    async def save_results(self, results: Dict[str, Any], output_dir: str = "data/processed"):
        """Save scraping results in multiple formats"""
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save raw Reddit data (JSON)
        raw_file = os.path.join(output_dir, f"reddit_singapore_housing_raw_{timestamp}.json")
        with open(raw_file, 'w', encoding='utf-8') as f:
            json.dump(results['raw_posts'], f, indent=2, ensure_ascii=False)

        # Save Q&A format (JSON)
        qa_file = os.path.join(output_dir, f"reddit_singapore_housing_qa_{timestamp}.json")
        with open(qa_file, 'w', encoding='utf-8') as f:
            json.dump(results['qa_format'], f, indent=2, ensure_ascii=False)

        # Save CSV format
        csv_file = os.path.join(output_dir, f"reddit_singapore_housing_csv_{timestamp}.csv")
        csv_data = results['csv_format']

        if csv_data:
            # Find all unique answer columns
            all_columns = set()
            for row in csv_data:
                all_columns.update(row.keys())

            # Sort columns: question first, then answer1, answer2, etc.
            answer_columns = sorted([col for col in all_columns if col.startswith('answer')],
                                  key=lambda x: int(x.replace('answer', '')))
            columns = ['question'] + answer_columns

            with open(csv_file, 'w', encoding='utf-8') as f:
                # Write headers
                f.write(','.join(f'"{col}"' for col in columns) + '\n')

                # Write data
                for row in csv_data:
                    csv_row = []
                    for col in columns:
                        value = row.get(col, '').replace('"', '""')  # Escape quotes
                        csv_row.append(f'"{value}"')
                    f.write(','.join(csv_row) + '\n')

        print(f"\n📁 Results saved:")
        print(f"   • Raw data: {raw_file}")
        print(f"   • Q&A format: {qa_file}")
        print(f"   • CSV format: {csv_file}")

        return {
            'raw_file': raw_file,
            'qa_file': qa_file,
            'csv_file': csv_file
        }

async def main():
    """Main function to run Reddit scraping"""
    scraper = RedditScraper()

    try:
        # Scrape all Reddit URLs
        results = await scraper.scrape_all_reddit_urls()

        # Save results
        files = await scraper.save_results(results)

        print(f"\n✅ Reddit scraping completed successfully!")
        print(f"🎯 Ready to combine with PropertyGuru data for ML validation!")

        return files

    except Exception as e:
        print(f"\n💥 Error during Reddit scraping: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Reddit Singapore Housing Scraper                         ║
║                                                                              ║
║  🎯 Scraping specific Reddit URLs for Singapore housing/BTO discussions     ║
║  📊 Output format: question, answer1, answer2, answer3, ... (unlimited)     ║
║  🔄 Ready to combine with PropertyGuru data for ML validation               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏸️  Reddit scraping interrupted by user")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")