#!/usr/bin/env python3
"""
Comprehensive Data Combiner - Merge ALL PropertyGuru + Reddit data
Expected: ~400 total questions from both sources combined
"""

import json
import csv
import os
import pandas as pd
from datetime import datetime
import glob

def load_propertyguru_data():
    """Load PropertyGuru data from the final combined file"""
    print("🏠 Loading PropertyGuru data...")
    
    # Look for the PropertyGuru combined file
    pg_file = "data/final/propertyguru_ALL_COMBINED_20250715_135521.json"
    
    if not os.path.exists(pg_file):
        print(f"   ❌ PropertyGuru file not found: {pg_file}")
        return []
    
    try:
        with open(pg_file, 'r', encoding='utf-8') as f:
            pg_data = json.load(f)
        
        # Extract the questions from the data structure
        questions = pg_data.get('questions', [])
        print(f"   ✅ Loaded {len(questions)} PropertyGuru entries")
        return questions
        
    except Exception as e:
        print(f"   ❌ Error loading PropertyGuru data: {e}")
        return []

def load_reddit_data():
    """Load all Reddit data files from processed folder"""
    print("🔍 Loading Reddit data...")
    
    all_reddit_data = []
    
    # Find all Reddit JSON files in processed folder
    reddit_files = glob.glob("data/processed/reddit_*.json")
    
    if not reddit_files:
        print("   ❌ No Reddit files found")
        return []
    
    print(f"   📁 Found {len(reddit_files)} Reddit files")
    
    for file_path in reddit_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list) and len(data) > 0:
                all_reddit_data.extend(data)
                print(f"   ✅ Loaded {len(data)} entries from {os.path.basename(file_path)}")
            else:
                print(f"   ⚠️  Empty or invalid format in {os.path.basename(file_path)}")
                
        except Exception as e:
            print(f"   ❌ Error loading {os.path.basename(file_path)}: {e}")
    
    print(f"   🎯 Total Reddit entries: {len(all_reddit_data)}")
    return all_reddit_data

def convert_propertyguru_to_unified_format(pg_data):
    """Convert PropertyGuru data to unified format"""
    print("🔄 Converting PropertyGuru data to unified format...")
    
    unified_data = []
    
    for entry in pg_data:
        try:
            # Extract question text
            question = entry.get('text', '').strip()
            if not question:
                continue
            
            # Extract answers
            answers_raw = entry.get('answers', [])
            answers = []
            
            for ans in answers_raw:
                if isinstance(ans, dict):
                    answer_text = ans.get('text', '').strip()
                    if answer_text:
                        answers.append(answer_text)
                elif isinstance(ans, str):
                    answer_text = ans.strip()
                    if answer_text:
                        answers.append(answer_text)
            
            if not answers:
                continue
            
            # Create unified entry
            unified_entry = {
                'source': 'PropertyGuru',
                'question': question,
                'answers': answers,
                'metadata': {
                    'url': entry.get('url', ''),
                    'title': entry.get('title', ''),
                    'author': entry.get('author', ''),
                    'date': entry.get('date', ''),
                    'answer_count': len(answers)
                }
            }
            
            unified_data.append(unified_entry)
                
        except Exception as e:
            print(f"   ⚠️  Error processing PropertyGuru entry: {e}")
            continue
    
    print(f"   ✅ Converted {len(unified_data)} PropertyGuru entries")
    return unified_data

def convert_reddit_to_unified_format(reddit_data):
    """Convert Reddit data to unified format"""
    print("🔄 Converting Reddit data to unified format...")
    
    unified_data = []
    
    for entry in reddit_data:
        try:
            question = entry.get('question', '').strip()
            comments = entry.get('comments', [])
            
            if not question or not comments:
                continue
            
            # Extract answer texts from comments
            answers = []
            for comment in comments:
                if isinstance(comment, dict):
                    answer_text = comment.get('text', '').strip()
                elif isinstance(comment, str):
                    answer_text = comment.strip()
                else:
                    continue
                
                if answer_text:
                    answers.append(answer_text)
            
            if not answers:
                continue
            
            # Create unified entry
            unified_entry = {
                'source': 'Reddit',
                'question': question,
                'answers': answers,
                'metadata': {
                    'url': entry.get('url', ''),
                    'title': entry.get('title', ''),
                    'subreddit': entry.get('subreddit', ''),
                    'author': entry.get('author', ''),
                    'score': entry.get('score', 0),
                    'scraped_at': entry.get('scraped_at', ''),
                    'answer_count': len(answers)
                }
            }
            
            unified_data.append(unified_entry)
                
        except Exception as e:
            print(f"   ⚠️  Error processing Reddit entry: {e}")
            continue
    
    print(f"   ✅ Converted {len(unified_data)} Reddit entries")
    return unified_data

def create_combined_schema(unified_data):
    """Create the requested schema: question, answer_1, answer_2, ..., answer_x"""
    print("🎯 Creating combined schema format...")
    
    # Find maximum number of answers
    max_answers = 0
    for entry in unified_data:
        if len(entry['answers']) > max_answers:
            max_answers = len(entry['answers'])
    
    print(f"   📊 Maximum answers per question: {max_answers}")
    
    # Create the combined schema data
    combined_data = []
    
    for entry in unified_data:
        combined_entry = {
            'question': entry['question'],
            'source': entry['source'],
            'answer_count': len(entry['answers']),
            'url': entry['metadata'].get('url', ''),
            'title': entry['metadata'].get('title', ''),
            'author': entry['metadata'].get('author', ''),
        }
        
        # Add Reddit-specific fields
        if entry['source'] == 'Reddit':
            combined_entry['subreddit'] = entry['metadata'].get('subreddit', '')
            combined_entry['score'] = entry['metadata'].get('score', 0)
        else:
            combined_entry['subreddit'] = ''
            combined_entry['score'] = 0
        
        # Add individual answers as separate columns
        for i in range(1, max_answers + 1):
            if i <= len(entry['answers']):
                combined_entry[f'answer_{i}'] = entry['answers'][i-1]
            else:
                combined_entry[f'answer_{i}'] = ''
        
        combined_data.append(combined_entry)
    
    print(f"   ✅ Created {len(combined_data)} entries with {max_answers} answer columns")
    return combined_data, max_answers

def save_combined_data(combined_data, max_answers):
    """Save the combined data in CSV and JSON formats"""
    print("💾 Saving combined datasets...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = "data/combined"
    os.makedirs(output_dir, exist_ok=True)
    
    # Save CSV format (your requested schema)
    csv_file = f"{output_dir}/ALL_COMBINED_SCHEMA_{timestamp}.csv"
    json_file = f"{output_dir}/ALL_COMBINED_SCHEMA_{timestamp}.json"
    
    # Create DataFrame with proper column ordering
    df = pd.DataFrame(combined_data)
    
    # Reorder columns: question first, then metadata, then all answer columns
    base_columns = ['question', 'source', 'answer_count', 'url', 'title', 'author', 'subreddit', 'score']
    answer_columns = [f'answer_{i}' for i in range(1, max_answers + 1)]
    
    column_order = base_columns + answer_columns
    existing_columns = [col for col in column_order if col in df.columns]
    df = df[existing_columns]
    
    # Save files
    df.to_csv(csv_file, index=False, encoding='utf-8')
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(combined_data, f, indent=2, ensure_ascii=False)
    
    return csv_file, json_file

def main():
    """Main function to combine all datasets"""
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    ALL DATA COMBINER - PropertyGuru + Reddit                 ║
║                                                                              ║
║  🏠 PropertyGuru: ~2,400 expert Q&A entries                                 ║
║  🔍 Reddit: ~100+ community discussions                                     ║
║  🎯 Target: ~400+ total questions combined                                   ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Step 1: Load all data
    pg_data = load_propertyguru_data()
    reddit_data = load_reddit_data()
    
    if not pg_data and not reddit_data:
        print("❌ No data found to combine!")
        return None
    
    # Step 2: Convert to unified format
    unified_pg = convert_propertyguru_to_unified_format(pg_data)
    unified_reddit = convert_reddit_to_unified_format(reddit_data)
    
    # Step 3: Combine all data
    unified_data = unified_pg + unified_reddit
    print(f"\n🎯 Total unified entries: {len(unified_data)}")
    
    # Step 4: Create combined schema
    combined_data, max_answers = create_combined_schema(unified_data)
    
    # Step 5: Save datasets
    csv_file, json_file = save_combined_data(combined_data, max_answers)
    
    # Step 6: Generate final statistics
    pg_count = len(unified_pg)
    reddit_count = len(unified_reddit)
    total_answers = sum(len(entry['answers']) for entry in unified_data)
    
    print(f"""
🎉 ALL DATA COMBINATION COMPLETED SUCCESSFULLY!

📊 Final Statistics:
   • Total Questions: {len(unified_data):,}
   • PropertyGuru Questions: {pg_count:,}
   • Reddit Questions: {reddit_count:,}
   • Total Answers: {total_answers:,}
   • Max Answers per Question: {max_answers}
   • Avg Answers per Question: {total_answers/len(unified_data):.1f}

📁 Files Created:
   • 🎯 COMBINED SCHEMA CSV: {os.path.basename(csv_file)}
   • 🎯 COMBINED SCHEMA JSON: {os.path.basename(json_file)}

🚀 Ready for ML Training!
   • Schema: question, answer_1, answer_2, ..., answer_{max_answers}
   • All data sources combined in one file
   • Perfect for your ML validation pipeline
    """)
    
    return csv_file, json_file

if __name__ == "__main__":
    main()
