#!/usr/bin/env python3
"""
Count all individual text strings across all datasets
"""

import json
import csv
import os

def count_propertyguru_data():
    """Count PropertyGuru data points"""
    print("📊 Counting PropertyGuru Data...")
    
    # Load the comprehensive JSON data
    json_file = "data/final/propertyguru_ALL_COMBINED_20250715_135521.json"
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    questions = data.get('questions', [])
    
    total_questions = len(questions)
    total_answers = 0
    
    for question in questions:
        answers = question.get('answers', [])
        total_answers += len(answers)
    
    total_propertyguru = total_questions + total_answers
    
    print(f"   • Questions: {total_questions:,}")
    print(f"   • Answers: {total_answers:,}")
    print(f"   • Total PropertyGuru strings: {total_propertyguru:,}")
    
    return {
        'questions': total_questions,
        'answers': total_answers,
        'total': total_propertyguru
    }

def count_reddit_data():
    """Count Reddit data points"""
    print("\n📊 Counting Reddit Data...")
    
    # Find the latest Reddit JSON file
    reddit_files = []
    for file in os.listdir("data/processed"):
        if file.startswith("reddit_json_") and file.endswith(".json"):
            reddit_files.append(file)
    
    if not reddit_files:
        print("   ❌ No Reddit data found")
        return {'questions': 0, 'answers': 0, 'total': 0}
    
    # Use the latest file
    latest_file = sorted(reddit_files)[-1]
    reddit_file = f"data/processed/{latest_file}"
    
    with open(reddit_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    total_questions = len(data)
    total_answers = 0
    
    for post in data:
        comments = post.get('comments', [])
        total_answers += len(comments)
    
    total_reddit = total_questions + total_answers
    
    print(f"   • Questions: {total_questions:,}")
    print(f"   • Answers: {total_answers:,}")
    print(f"   • Total Reddit strings: {total_reddit:,}")
    
    return {
        'questions': total_questions,
        'answers': total_answers,
        'total': total_reddit
    }

def count_demo_data():
    """Count demo data from archive"""
    print("\n📊 Counting Demo Data (Archive)...")
    
    demo_files = [
        "data/archive/propertyguru_mvp_demo.json",
        "data/archive/reddit_mvp_demo.json",
        "data/archive/hardwarezone_mvp_demo.json",
        "data/archive/bto_processed_reviews.json"
    ]
    
    total_demo = 0
    demo_breakdown = {}
    
    for file_path in demo_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    count = len(data)
                elif isinstance(data, dict):
                    if 'questions' in data:
                        # PropertyGuru format
                        questions = data.get('questions', [])
                        answers = sum(len(q.get('answers', [])) for q in questions)
                        count = len(questions) + answers
                    else:
                        count = 1
                else:
                    count = 1
                
                source = os.path.basename(file_path).replace('_mvp_demo.json', '').replace('_processed_reviews.json', '')
                demo_breakdown[source] = count
                total_demo += count
                
                print(f"   • {source}: {count:,}")
                
            except Exception as e:
                print(f"   ❌ Error reading {file_path}: {e}")
    
    print(f"   • Total Demo strings: {total_demo:,}")
    
    return {
        'breakdown': demo_breakdown,
        'total': total_demo
    }

def main():
    """Main counting function"""
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Complete Data Count Analysis                              ║
║                                                                              ║
║  🎯 Counting all individual text strings across all datasets                ║
║  📊 Each question = 1 string, each answer = 1 string                        ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Count all datasets
    propertyguru = count_propertyguru_data()
    reddit = count_reddit_data()
    demo = count_demo_data()
    
    # Calculate totals
    total_questions = propertyguru['questions'] + reddit['questions']
    total_answers = propertyguru['answers'] + reddit['answers']
    total_main_data = propertyguru['total'] + reddit['total']
    total_all_data = total_main_data + demo['total']
    
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                            FINAL DATA COUNT                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝

📊 MAIN DATASETS (for ML validation):
   • Total Questions: {total_questions:,}
   • Total Answers: {total_answers:,}
   • Total Main Data Strings: {total_main_data:,}

📈 BREAKDOWN:
   • PropertyGuru Questions: {propertyguru['questions']:,}
   • PropertyGuru Answers: {propertyguru['answers']:,}
   • Reddit Questions: {reddit['questions']:,}
   • Reddit Answers: {reddit['answers']:,}

📚 DEMO DATA (archive):
   • Demo Data Strings: {demo['total']:,}

🎯 GRAND TOTAL:
   • All Data Strings: {total_all_data:,}

🚀 READY FOR ML VALIDATION:
   • Primary Dataset: {total_main_data:,} strings
   • Singapore Housing Context: ✅
   • Question-Answer Format: ✅
   • BERT Training Ready: ✅
    """)
    
    # Save summary
    summary = {
        'main_datasets': {
            'propertyguru': propertyguru,
            'reddit': reddit,
            'total_questions': total_questions,
            'total_answers': total_answers,
            'total_strings': total_main_data
        },
        'demo_data': demo,
        'grand_total': total_all_data,
        'timestamp': '2025-07-16'
    }
    
    with open('data_count_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"📁 Summary saved to: data_count_summary.json")
    
    return summary

if __name__ == "__main__":
    main()
