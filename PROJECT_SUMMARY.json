{"project": "PropertyGuru HDB Questions Scraper", "description": "Comprehensive scraping and ML dataset preparation for PropertyGuru HDB questions and answers", "created": "2025-07-16T04:30:01.540088", "structure": {"src/": {"description": "Main source code directory", "subdirectories": {"scrapers/": {"description": "Web scraping modules", "subdirectories": {"propertyguru/": "PropertyGuru expert Q&A scraper", "reddit/": "Reddit community discussion scraper", "hardwarezone/": "HardwareZone forum scraper"}}, "data_processing/": {"description": "Data processing and combination scripts", "files": {"combine_all_datasets.py": "Main data combination script"}}, "utils/": {"description": "Utility functions and helpers", "files": {"scraper_helper.py": "Common scraping utilities", "gemini_processor.py": "AI processing utilities"}}}}, "data/": {"description": "Data storage directory", "subdirectories": {"combined/": "Final combined datasets (ALL_COMBINED_SCHEMA_*.csv)", "final/": "PropertyGuru processed data", "processed/": "Reddit processed data", "archive/": "Historical/backup data"}}, "tools/": {"description": "Development tools and utilities", "files": {"start_distributed_scraping.sh": "Auto-launch script for distributed scraping"}, "subdirectories": {"legacy_scrapers/": "Old scraper versions for reference"}}, "docs/": {"description": "Project documentation", "files": {"PROJECT_PLAN.md": "Original project plan and requirements"}}, "ml_validation/": {"description": "Machine learning validation pipeline", "subdirectories": {"notebooks/": "Jupyter notebooks for analysis", "results/": "ML validation results"}}}, "dataset_stats": {"total_questions": 2180, "propertyguru_questions": 2056, "reddit_questions": 124, "total_answers": 9423, "date_range": "April 2013 - July 2025", "avg_answers_per_question": 4.3, "max_answers_per_question": 25, "schema_format": "question, answer_1, answer_2, ..., answer_25"}, "usage": {"main_combined_dataset": "data/combined/ALL_COMBINED_SCHEMA_20250716_075120.csv", "data_combination_script": "src/data_processing/combine_all_datasets.py", "recommended_propertyguru_scraper": "src/scrapers/propertyguru/distributed_propertyguru_scraper.py", "reddit_scraper": "src/scrapers/reddit/reddit_scraper.py"}, "next_steps": ["Use data/combined/ALL_COMBINED_SCHEMA_*.csv for ML training", "Test Singapore BERT vs Baseline BERT using combined dataset", "Implement ML models for answer quality prediction", "Create recommendation system using the combined data", "Expand Reddit data collection for more community insights"]}