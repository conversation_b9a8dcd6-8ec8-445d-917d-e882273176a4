#!/usr/bin/env python3
"""
Singapore Housing Sentiment Analysis Pipeline
Compares Singapore BERT vs Baseline BERT for sentiment analysis on housing discussions
"""

import pandas as pd
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from datasets import Dataset
import torch
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
import re
from google.cloud import storage
import json

class SingaporeHousingSentimentAnalyzer:
    def __init__(self, model_name="bert-base-uncased"):
        self.model_name = model_name
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(
            model_name, 
            num_labels=4  # positive, negative, neutral, mixed
        )
        
        # Singapore housing sentiment patterns
        self.singapore_patterns = {
            'positive': [
                r'\b(shiok|steady|good deal|worth it|happy|excited|finally got)\b',
                r'\b(successful|approved|lucky|grateful|satisfied)\b'
            ],
            'negative': [
                r'\b(sian|wah lau|damn|frustrated|disappointed|expensive)\b',
                r'\b(rejected|failed|cannot afford|stress|worried|angry)\b',
                r'\b(COV.*high|BTO.*hard|price.*crazy)\b'
            ],
            'neutral': [
                r'\b(how to|what is|anyone know|procedure|process)\b',
                r'\b(information|details|requirements|eligibility)\b'
            ]
        }
    
    def load_data(self, csv_path):
        """Load and preprocess Singapore housing data"""
        print("📊 Loading Singapore housing dataset...")
        df = pd.read_csv(csv_path)
        
        # Combine question and answers for sentiment analysis
        texts = []
        sources = []
        
        for _, row in df.iterrows():
            # Add question
            texts.append(row['question'])
            sources.append(row['source'])
            
            # Add all answers
            for i in range(1, 26):  # answer_1 to answer_25
                answer_col = f'answer_{i}'
                if answer_col in row and pd.notna(row[answer_col]) and row[answer_col].strip():
                    texts.append(row[answer_col])
                    sources.append(row['source'])
        
        print(f"   ✅ Loaded {len(texts)} text samples")
        return texts, sources
    
    def create_sentiment_labels(self, texts):
        """Create sentiment labels using Singapore-specific patterns"""
        print("🏷️ Creating sentiment labels...")
        labels = []
        
        for text in texts:
            text_lower = text.lower()
            
            # Check for Singapore-specific sentiment patterns
            positive_score = sum(len(re.findall(pattern, text_lower)) 
                               for pattern in self.singapore_patterns['positive'])
            negative_score = sum(len(re.findall(pattern, text_lower)) 
                               for pattern in self.singapore_patterns['negative'])
            neutral_score = sum(len(re.findall(pattern, text_lower)) 
                              for pattern in self.singapore_patterns['neutral'])
            
            # Determine sentiment based on pattern matching
            if positive_score > negative_score and positive_score > 0:
                if negative_score > 0:
                    labels.append(3)  # mixed
                else:
                    labels.append(0)  # positive
            elif negative_score > positive_score and negative_score > 0:
                labels.append(1)  # negative
            elif neutral_score > 0 or (positive_score == negative_score == 0):
                labels.append(2)  # neutral
            else:
                labels.append(3)  # mixed
        
        print(f"   ✅ Created {len(labels)} sentiment labels")
        return labels
    
    def preprocess_for_bert(self, texts, labels):
        """Preprocess texts for BERT training"""
        print("🔤 Preprocessing for BERT...")
        
        encodings = self.tokenizer(
            texts,
            truncation=True,
            padding=True,
            max_length=512,
            return_tensors='pt'
        )
        
        dataset = Dataset.from_dict({
            'input_ids': encodings['input_ids'],
            'attention_mask': encodings['attention_mask'],
            'labels': labels
        })
        
        return dataset
    
    def train_model(self, train_dataset, val_dataset, output_dir):
        """Train BERT model for sentiment analysis"""
        print("🚀 Training sentiment analysis model...")
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=3,
            per_device_train_batch_size=16,
            per_device_eval_batch_size=16,
            warmup_steps=500,
            weight_decay=0.01,
            logging_dir=f'{output_dir}/logs',
            evaluation_strategy="epoch",
            save_strategy="epoch",
            load_best_model_at_end=True,
        )
        
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            compute_metrics=self.compute_metrics,
        )
        
        trainer.train()
        return trainer
    
    def compute_metrics(self, eval_pred):
        """Compute sentiment analysis metrics"""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        
        return {
            'accuracy': accuracy_score(labels, predictions),
            'f1_macro': f1_score(labels, predictions, average='macro'),
            'f1_weighted': f1_score(labels, predictions, average='weighted')
        }
    
    def evaluate_singapore_context(self, model, test_texts, test_labels):
        """Evaluate model on Singapore-specific sentiment patterns"""
        print("🇸🇬 Evaluating Singapore context understanding...")
        
        # Filter for texts with Singapore-specific terms
        singapore_terms = ['BTO', 'HDB', 'COV', 'CPF', 'lah', 'sia', 'shiok', 'sian']
        singapore_indices = []
        
        for i, text in enumerate(test_texts):
            if any(term.lower() in text.lower() for term in singapore_terms):
                singapore_indices.append(i)
        
        if singapore_indices:
            sg_texts = [test_texts[i] for i in singapore_indices]
            sg_labels = [test_labels[i] for i in singapore_indices]
            
            # Get predictions
            encodings = self.tokenizer(sg_texts, truncation=True, padding=True, 
                                     max_length=512, return_tensors='pt')
            
            with torch.no_grad():
                outputs = model(**encodings)
                predictions = torch.argmax(outputs.logits, dim=-1)
            
            accuracy = accuracy_score(sg_labels, predictions)
            f1 = f1_score(sg_labels, predictions, average='macro')
            
            print(f"   🎯 Singapore context accuracy: {accuracy:.3f}")
            print(f"   🎯 Singapore context F1: {f1:.3f}")
            
            return accuracy, f1
        
        return 0, 0

def compare_models():
    """Compare Singapore BERT vs Baseline BERT"""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║              SINGAPORE HOUSING SENTIMENT ANALYSIS COMPARISON                 ║
║                                                                              ║
║  🇸🇬 Singapore BERT vs 🌍 Baseline BERT                                      ║
║  📊 Dataset: 2,180 questions + 9,423 answers                                ║
║  🎯 Focus: Singapore housing sentiment patterns                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Load data
    data_path = "gs://singapore-housing-ml-data/datasets/singapore_housing_combined.csv"
    
    # Test baseline BERT
    print("\n🌍 Testing Baseline BERT...")
    baseline_analyzer = SingaporeHousingSentimentAnalyzer("bert-base-uncased")
    
    # Test Singapore BERT (replace with actual Singapore BERT model)
    print("\n🇸🇬 Testing Singapore BERT...")
    singapore_analyzer = SingaporeHousingSentimentAnalyzer("bert-base-uncased")  # Replace with Singapore BERT
    
    # Compare results
    print("\n📊 COMPARISON RESULTS:")
    print("=" * 50)
    print("Baseline BERT vs Singapore BERT sentiment analysis performance")
    print("Focus: Singapore housing discussions with local context")

if __name__ == "__main__":
    compare_models()
