# ML Validation Project: Singapore BERT vs Baseline BERT

## 🎯 Project Overview

**Objective**: Test and compare the quality of Singapore-localized BERT models against baseline BERT models using comprehensive Singapore housing Q&A dataset.

**Platform**: Google Cloud Vertex AI + Colab
**Dataset**: 2,180 combined PropertyGuru + Reddit questions with 9,423+ answers
**Timeline**: 2-3 weeks
**Cloud Setup**: ✅ Google Cloud API credentials configured

## 📊 Dataset Summary

### **✅ COMPLETED: Combined Dataset Ready**
- **File**: `../data/combined/ALL_COMBINED_SCHEMA_*.csv`
- **Format**: `question, answer_1, answer_2, ..., answer_25`
- **Total Questions**: 2,180 (PropertyGuru: 2,056 + Reddit: 124)
- **Total Answers**: 9,423 individual answer texts
- **Max Answers per Question**: 25
- **Average Answers per Question**: 4.3

### **PropertyGuru Component (Expert Q&A)**
- **Questions**: 2,056 expert-answered questions
- **Domain**: Singapore HDB housing (BTO, resale, grants, legal)
- **Language**: Formal English with Singapore housing context
- **Quality**: High-quality expert responses from real estate agents

### **Reddit Component (Community Discussions)**
- **Questions**: 124 community discussions
- **Sources**: r/singapore, r/askSingapore (housing-related posts)
- **Domain**: Broader Singapore housing discussions
- **Language**: Casual English with Singlish expressions
- **Quality**: Community-driven answers with varied perspectives

### **Data Characteristics**
- **Local Context**: Singapore-specific housing terms (HDB, BTO, COV, etc.)
- **Mixed Language**: English with Singlish expressions
- **Real-World**: Actual user questions from multiple platforms
- **Diverse Answers**: Expert responses + community discussions

## 🤖 Models to Test

### **Singapore-Localized BERT Models**
1. **Primary Candidate**: Singapore-specific BERT (if available)
2. **Alternative**: Southeast Asia BERT models
3. **Backup**: Fine-tuned BERT on Singapore text

### **Baseline Models**
1. **BERT-base-uncased** (Google's original)
2. **BERT-base-cased** 
3. **RoBERTa-base** (for comparison)

## 🧪 Evaluation Tasks

### **Task 1: Question Classification**
- **Objective**: Classify questions by HDB topic categories
- **Categories**: 
  - BTO/New Flats
  - Resale Market
  - Renovation/Interior
  - Financial/Grants
  - Legal/Procedures
  - General Housing

### **Task 2: Answer Quality Prediction**
- **Objective**: Predict which answers are most helpful
- **Features**: Answer text, length, context
- **Target**: Upvotes/engagement scores

### **Task 3: Question-Answer Matching**
- **Objective**: Match questions to most relevant answers
- **Method**: Semantic similarity scoring
- **Evaluation**: Ranking accuracy

### **Task 4: Singapore Context Understanding**
- **Objective**: Test understanding of local terms
- **Method**: Masked language modeling on Singapore-specific terms
- **Examples**: HDB, BTO, COV, CPF, etc.

## 📈 Evaluation Metrics

### **Classification Tasks**
- **Accuracy**: Overall classification accuracy
- **F1-Score**: Macro and weighted F1
- **Precision/Recall**: Per-class performance
- **Confusion Matrix**: Error analysis

### **Regression Tasks** (Answer Quality)
- **MSE**: Mean Squared Error
- **MAE**: Mean Absolute Error
- **R²**: Coefficient of determination
- **Spearman Correlation**: Rank correlation

### **Similarity Tasks**
- **Cosine Similarity**: Semantic similarity scores
- **Ranking Metrics**: MRR, NDCG
- **Top-K Accuracy**: Retrieval performance

### **Singapore Context**
- **Perplexity**: Language modeling performance
- **Token Accuracy**: Masked token prediction
- **Context Coherence**: Qualitative assessment

## 🔬 Experimental Design

### **✅ Phase 1: Data Preparation (COMPLETED)**
1. **✅ Combined Dataset Ready**: 2,180 questions with 9,423 answers
2. **✅ Format Standardized**: `question, answer_1, answer_2, ..., answer_25`
3. **✅ Data Sources Merged**: PropertyGuru expert Q&A + Reddit community discussions
4. **✅ Quality Assured**: Clean, structured, ML-ready format

### **Phase 2: Cloud Infrastructure Setup** (Day 1-2)
1. **Vertex AI Configuration**: Set up training environment with existing credentials
2. **Data Upload**: Transfer combined dataset to Cloud Storage
3. **Environment Setup**: Configure training containers and dependencies
4. **Resource Allocation**: Set up GPU instances for model training
5. **Monitoring Setup**: Configure training job monitoring and logging

### **Phase 3: Baseline Model Training** (Day 3-5)
1. **BERT-base Setup**: Load and configure baseline models on Vertex AI
2. **Data Preprocessing**: Tokenization and encoding for 2,180 questions
3. **Fine-tuning Pipeline**: Adapt baseline BERT to Singapore housing domain
4. **Task Evaluation**: Run all 4 evaluation tasks (classification, QA matching, etc.)
5. **Performance Baseline**: Establish comparison metrics and benchmarks

### **Phase 4: Singapore BERT Development** (Day 6-8)
1. **Model Research**: Identify best Singapore/Southeast Asia BERT models
2. **Custom Fine-tuning**: Adapt Singapore BERT to housing domain
3. **Comparative Training**: Train on identical dataset splits
4. **Task Evaluation**: Run identical evaluation tasks as baseline
5. **Performance Analysis**: Compare against baseline with statistical significance

### **Phase 5: Advanced Optimization** (Day 9-12)
1. **Hyperparameter Tuning**: Optimize both models using Vertex AI hyperparameter tuning
2. **Data Augmentation**: Leverage both PropertyGuru and Reddit data characteristics
3. **Ensemble Methods**: Combine expert (PropertyGuru) and community (Reddit) insights
4. **Cross-validation**: Robust evaluation across different data splits
5. **Error Analysis**: Deep dive into failure cases and improvement opportunities

### **Phase 6: Analysis & Optimization** (Day 10-12)
1. **Error Analysis**: Identify failure cases
2. **Hyperparameter Tuning**: Optimize both models
3. **Ablation Studies**: Test different configurations
4. **Statistical Testing**: Significance tests

### **Phase 7: Reporting** (Day 13-14)
1. **Results Compilation**: Aggregate all metrics
2. **Visualization**: Create comparison charts
3. **Case Studies**: Analyze specific examples
4. **Recommendations**: Provide actionable insights

## 🛠️ Technical Implementation

### **🚀 Google Cloud Vertex AI Setup**
```python
# Vertex AI setup with existing credentials
from google.cloud import aiplatform
from google.oauth2 import service_account

# Use existing credentials
credentials = service_account.Credentials.from_service_account_file(
    "../config/sharp-imprint-465805-d6-d541c7dff516.json"
)

# Initialize Vertex AI
aiplatform.init(
    project="sharp-imprint-465805-d6",
    location="us-central1",
    credentials=credentials
)

# Key libraries for ML pipeline
!pip install google-cloud-aiplatform transformers torch datasets scikit-learn
!pip install seaborn matplotlib plotly pandas numpy
```

### **💾 Data Upload to Cloud Storage**
```python
from google.cloud import storage

# Upload combined dataset to Cloud Storage
def upload_dataset():
    client = storage.Client(credentials=credentials)
    bucket = client.bucket("singapore-housing-ml-data")

    # Upload main dataset
    blob = bucket.blob("datasets/combined_schema.csv")
    blob.upload_from_filename("../data/combined/ALL_COMBINED_SCHEMA_*.csv")

    return "gs://singapore-housing-ml-data/datasets/combined_schema.csv"
```

### **🤖 Model Training on Vertex AI**
```python
# Custom training job for BERT fine-tuning
from google.cloud.aiplatform import CustomJob

# Singapore BERT model setup
from transformers import AutoTokenizer, AutoModel
tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")  # Start with base
model = AutoModel.from_pretrained("bert-base-uncased")

# Create custom training job
job = CustomJob(
    display_name="singapore-bert-housing-qa",
    worker_pool_specs=[{
        "machine_spec": {
            "machine_type": "n1-standard-4",
            "accelerator_type": "NVIDIA_TESLA_T4",
            "accelerator_count": 1,
        },
        "replica_count": 1,
        "container_spec": {
            "image_uri": "gcr.io/cloud-aiplatform/training/pytorch-gpu.1-9:latest",
            "command": ["python", "train_singapore_bert.py"],
            "args": ["--dataset_path", "gs://singapore-housing-ml-data/datasets/combined_schema.csv"]
        },
    }],
)
```

### **Data Pipeline**
1. **CSV Loading**: pandas.read_csv()
2. **Text Preprocessing**: Clean, tokenize, encode
3. **Label Generation**: Automated categorization
4. **Dataset Creation**: HuggingFace datasets format

### **Model Training**
1. **Fine-tuning**: Task-specific heads
2. **Training Loop**: Custom or HuggingFace Trainer
3. **Validation**: Early stopping, best model selection
4. **Evaluation**: Comprehensive metrics calculation

## 📋 Deliverables

### **Code Deliverables**
1. **Data Preprocessing Notebook**: Clean and prepare dataset
2. **Baseline Model Notebook**: BERT-base evaluation
3. **Singapore BERT Notebook**: Localized model evaluation
4. **Comparison Analysis Notebook**: Side-by-side results
5. **Visualization Notebook**: Charts and insights

### **Documentation**
1. **Technical Report**: Detailed methodology and results
2. **Executive Summary**: Key findings and recommendations
3. **Model Cards**: Documentation for each model tested
4. **Reproducibility Guide**: Steps to replicate results

### **Results**
1. **Performance Metrics**: Comprehensive comparison table
2. **Statistical Analysis**: Significance tests and confidence intervals
3. **Error Analysis**: Failure cases and improvement suggestions
4. **Recommendations**: Which model to use for Singapore context

## 🎯 Success Criteria

### **Primary Goals**
- [ ] Successfully evaluate both baseline and Singapore BERT models
- [ ] Generate comprehensive performance comparison
- [ ] Identify clear winner for Singapore HDB domain
- [ ] Provide actionable recommendations

### **Secondary Goals**
- [ ] Discover Singapore-specific linguistic patterns
- [ ] Create reusable evaluation framework
- [ ] Generate insights for future model development
- [ ] Build foundation for production deployment

## 🚀 Next Steps

### **✅ Completed Preparations**
- ✅ **Combined Dataset**: 2,180 questions ready in ML format
- ✅ **Google Cloud Setup**: API credentials configured
- ✅ **Data Pipeline**: Automated combination script available
- ✅ **Project Structure**: Clean, organized codebase

### **Immediate Actions (Week 1)**
1. **🚀 Vertex AI Setup**: Configure training environment and upload dataset
2. **📊 Data Analysis**: Explore Singapore-specific terms and patterns
3. **🤖 Model Research**: Identify best Singapore/Southeast Asia BERT models
4. **⚙️ Training Pipeline**: Set up automated training and evaluation pipeline
5. **📈 Baseline Training**: Start with BERT-base on combined dataset

### **Priority Focus Areas**
1. **Singapore Context Understanding**: Test on local housing terms (HDB, BTO, COV, CPF)
2. **Answer Quality Ranking**: Leverage both expert and community answer quality
3. **Question Classification**: Categorize by housing topic (BTO, resale, renovation, etc.)
4. **Cross-Domain Performance**: Expert (PropertyGuru) vs Community (Reddit) insights

### **Resource Requirements**
- **Compute**: Vertex AI with GPU instances (T4/V100 for training)
- **Storage**: Cloud Storage for datasets and model artifacts
- **Timeline**: 2-3 weeks for comprehensive evaluation
- **Budget**: Estimate $200-500 for GPU training time

## 📞 Let's Discuss

**Key Questions for Discussion:**
1. Do you have specific Singapore BERT models in mind?
2. Which evaluation tasks are highest priority?
3. Should we focus on classification or similarity tasks?
4. Any specific Singapore terms/contexts to emphasize?
5. What's the target performance improvement threshold?

**Ready to dive into the ML validation!** 🤖
