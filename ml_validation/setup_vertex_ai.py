#!/usr/bin/env python3
"""
Vertex AI Setup Script for Singapore Housing ML Pipeline
Sets up Google Cloud Vertex AI environment for BERT model training
"""

import os
import sys
from google.cloud import aiplatform, storage
from google.oauth2 import service_account
import pandas as pd
import json

# Configuration
PROJECT_ID = "sharp-imprint-465805-d6"
LOCATION = "us-central1"
BUCKET_NAME = "singapore-housing-ml-data"
CREDENTIALS_PATH = "../config/sharp-imprint-465805-d6-d541c7dff516.json"

def setup_credentials():
    """Set up Google Cloud credentials"""
    print("🔐 Setting up Google Cloud credentials...")
    
    if not os.path.exists(CREDENTIALS_PATH):
        print(f"❌ Credentials file not found: {CREDENTIALS_PATH}")
        return None
    
    credentials = service_account.Credentials.from_service_account_file(CREDENTIALS_PATH)
    print("✅ Credentials loaded successfully")
    return credentials

def initialize_vertex_ai(credentials):
    """Initialize Vertex AI platform"""
    print("🚀 Initializing Vertex AI...")
    
    aiplatform.init(
        project=PROJECT_ID,
        location=LOCATION,
        credentials=credentials
    )
    print(f"✅ Vertex AI initialized for project: {PROJECT_ID}")

def create_storage_bucket(credentials):
    """Create Cloud Storage bucket for datasets"""
    print("💾 Setting up Cloud Storage...")
    
    client = storage.Client(credentials=credentials, project=PROJECT_ID)
    
    try:
        bucket = client.bucket(BUCKET_NAME)
        if not bucket.exists():
            bucket = client.create_bucket(BUCKET_NAME, location=LOCATION)
            print(f"✅ Created bucket: {BUCKET_NAME}")
        else:
            print(f"✅ Bucket already exists: {BUCKET_NAME}")
        return bucket
    except Exception as e:
        print(f"❌ Error creating bucket: {e}")
        return None

def upload_dataset(bucket):
    """Upload combined dataset to Cloud Storage"""
    print("📤 Uploading combined dataset...")
    
    # Find the latest combined dataset
    import glob
    dataset_files = glob.glob("../data/combined/ALL_COMBINED_SCHEMA_*.csv")
    
    if not dataset_files:
        print("❌ No combined dataset found. Run combine_data.py first!")
        return None
    
    latest_file = max(dataset_files, key=os.path.getctime)
    print(f"📁 Found dataset: {os.path.basename(latest_file)}")
    
    # Upload to Cloud Storage
    blob_name = "datasets/singapore_housing_combined.csv"
    blob = bucket.blob(blob_name)
    
    try:
        blob.upload_from_filename(latest_file)
        gcs_path = f"gs://{BUCKET_NAME}/{blob_name}"
        print(f"✅ Dataset uploaded to: {gcs_path}")
        
        # Also upload metadata
        df = pd.read_csv(latest_file)
        metadata = {
            "total_questions": len(df),
            "total_answers": sum(df['answer_count']),
            "max_answers_per_question": df['answer_count'].max(),
            "avg_answers_per_question": df['answer_count'].mean(),
            "propertyguru_questions": len(df[df['source'] == 'PropertyGuru']),
            "reddit_questions": len(df[df['source'] == 'Reddit']),
            "upload_timestamp": pd.Timestamp.now().isoformat()
        }
        
        metadata_blob = bucket.blob("datasets/metadata.json")
        metadata_blob.upload_from_string(json.dumps(metadata, indent=2))
        print("✅ Dataset metadata uploaded")
        
        return gcs_path
        
    except Exception as e:
        print(f"❌ Error uploading dataset: {e}")
        return None

def create_training_job_template():
    """Create template for custom training job"""
    print("📝 Creating training job template...")
    
    training_script = """
# Training script template for Singapore BERT
import os
import pandas as pd
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from datasets import Dataset
import torch

def load_data(gcs_path):
    # Load data from Cloud Storage
    df = pd.read_csv(gcs_path)
    return df

def preprocess_data(df):
    # Preprocess for BERT training
    # Implementation depends on specific task
    pass

def train_model():
    # Main training logic
    pass

if __name__ == "__main__":
    train_model()
"""
    
    os.makedirs("training_scripts", exist_ok=True)
    with open("training_scripts/train_singapore_bert.py", "w") as f:
        f.write(training_script)
    
    print("✅ Training script template created")

def setup_monitoring():
    """Set up monitoring and logging"""
    print("📊 Setting up monitoring...")
    
    # Create monitoring configuration
    monitoring_config = {
        "project_id": PROJECT_ID,
        "location": LOCATION,
        "bucket_name": BUCKET_NAME,
        "metrics_to_track": [
            "sentiment_accuracy",
            "sentiment_f1_macro",
            "sentiment_f1_weighted",
            "singapore_context_sentiment_accuracy",
            "singlish_sentiment_accuracy",
            "positive_sentiment_precision",
            "negative_sentiment_precision",
            "neutral_sentiment_precision",
            "mixed_sentiment_precision"
        ]
    }
    
    with open("monitoring_config.json", "w") as f:
        json.dump(monitoring_config, f, indent=2)
    
    print("✅ Monitoring configuration created")

def main():
    """Main setup function"""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║              VERTEX AI SETUP FOR SINGAPORE HOUSING SENTIMENT ANALYSIS      ║
║                                                                              ║
║  🏠 Dataset: 2,180 questions + 9,423 answers                                ║
║  🎭 Focus: Sentiment Analysis (positive/negative/neutral/mixed)             ║
║  🇸🇬 Singapore BERT vs 🌍 Baseline BERT                                      ║
║  ☁️  Platform: Google Cloud Vertex AI                                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Step 1: Setup credentials
    credentials = setup_credentials()
    if not credentials:
        return False
    
    # Step 2: Initialize Vertex AI
    initialize_vertex_ai(credentials)
    
    # Step 3: Create storage bucket
    bucket = create_storage_bucket(credentials)
    if not bucket:
        return False
    
    # Step 4: Upload dataset
    gcs_path = upload_dataset(bucket)
    if not gcs_path:
        return False
    
    # Step 5: Create training templates
    create_training_job_template()
    
    # Step 6: Setup monitoring
    setup_monitoring()
    
    print(f"""
🎉 VERTEX AI SETUP COMPLETED SUCCESSFULLY!

📊 Setup Summary:
   • Project ID: {PROJECT_ID}
   • Location: {LOCATION}
   • Bucket: gs://{BUCKET_NAME}
   • Dataset: {gcs_path}
   • Credentials: ✅ Configured

🚀 Next Steps:
   1. Review training_scripts/train_singapore_bert.py
   2. Customize training parameters for your use case
   3. Submit training job to Vertex AI
   4. Monitor training progress and results

💡 Ready to train Singapore BERT models on your housing dataset!
    """)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
