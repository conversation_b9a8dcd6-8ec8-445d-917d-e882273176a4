# 🚀 Vertex AI Quick Start Guide

## ✅ Prerequisites Completed
- **✅ Google Cloud Project**: `sharp-imprint-465805-d6` 
- **✅ API Credentials**: Located at `../config/sharp-imprint-*.json`
- **✅ Combined Dataset**: 2,180 questions ready in `../data/combined/`
- **✅ Clean Codebase**: Organized structure with ML pipeline

## 🏃‍♂️ Quick Start (5 Minutes)

### **Step 1: Install Dependencies**
```bash
cd ml_validation
pip install -r requirements.txt
```

### **Step 2: Run Vertex AI Setup**
```bash
python3 setup_vertex_ai.py
```

This will:
- ✅ Configure Google Cloud credentials
- ✅ Initialize Vertex AI platform
- ✅ Create Cloud Storage bucket
- ✅ Upload your combined dataset (2,180 questions)
- ✅ Create training script templates
- ✅ Set up monitoring configuration

### **Step 3: Verify Setup**
```bash
# Check if dataset was uploaded successfully
gsutil ls gs://singapore-housing-ml-data/datasets/

# View dataset metadata
gsutil cat gs://singapore-housing-ml-data/datasets/metadata.json
```

## 🤖 Training Singapore BERT

### **Option 1: Custom Training Job**
```python
from google.cloud import aiplatform

# Create custom training job
job = aiplatform.CustomJob(
    display_name="singapore-bert-housing-qa",
    worker_pool_specs=[{
        "machine_spec": {
            "machine_type": "n1-standard-4",
            "accelerator_type": "NVIDIA_TESLA_T4",
            "accelerator_count": 1,
        },
        "replica_count": 1,
        "container_spec": {
            "image_uri": "gcr.io/cloud-aiplatform/training/pytorch-gpu.1-9:latest",
            "command": ["python", "train_singapore_bert.py"],
            "args": [
                "--dataset_path", "gs://singapore-housing-ml-data/datasets/singapore_housing_combined.csv",
                "--model_name", "bert-base-uncased",
                "--task", "question_classification"
            ]
        },
    }],
)

# Submit job
job.run()
```

### **Option 2: AutoML Text Classification**
```python
# For quick prototyping
dataset = aiplatform.TextDataset.create(
    display_name="singapore-housing-questions",
    gcs_source="gs://singapore-housing-ml-data/datasets/singapore_housing_combined.csv"
)

job = aiplatform.AutoMLTextTrainingJob(
    display_name="singapore-housing-automl",
    prediction_type="classification"
)

model = job.run(
    dataset=dataset,
    training_fraction_split=0.8,
    validation_fraction_split=0.1,
    test_fraction_split=0.1,
)
```

## 📊 Your Dataset Overview

```json
{
  "total_questions": 2180,
  "total_answers": 9423,
  "max_answers_per_question": 25,
  "avg_answers_per_question": 4.3,
  "propertyguru_questions": 2056,
  "reddit_questions": 124,
  "schema": "question, answer_1, answer_2, ..., answer_25"
}
```

## 🎯 Recommended Training Tasks

### **1. Question Classification**
- **Goal**: Categorize housing questions (BTO, resale, renovation, etc.)
- **Input**: Question text
- **Output**: Category label
- **Metric**: F1-score, accuracy

### **2. Answer Quality Ranking**
- **Goal**: Rank answers by helpfulness
- **Input**: Question + multiple answers
- **Output**: Quality scores
- **Metric**: Ranking correlation

### **3. Singapore Context Understanding**
- **Goal**: Test local housing term comprehension
- **Input**: Questions with Singapore-specific terms
- **Output**: Contextual understanding score
- **Metric**: Masked language modeling accuracy

## 💰 Cost Estimation

### **Training Costs (Vertex AI)**
- **T4 GPU**: ~$0.35/hour
- **V100 GPU**: ~$2.48/hour
- **Typical Training**: 2-8 hours
- **Estimated Cost**: $5-50 per experiment

### **Storage Costs**
- **Dataset Storage**: <$1/month
- **Model Artifacts**: $2-5/month

## 🔍 Monitoring Your Training

### **Vertex AI Console**
1. Go to [Vertex AI Console](https://console.cloud.google.com/vertex-ai)
2. Navigate to "Training" → "Custom Jobs"
3. Monitor training progress and logs

### **TensorBoard Integration**
```python
# Add to your training script
from torch.utils.tensorboard import SummaryWriter
writer = SummaryWriter('gs://singapore-housing-ml-data/tensorboard/')
```

## 🚨 Troubleshooting

### **Common Issues**
1. **Quota Exceeded**: Request GPU quota increase in Cloud Console
2. **Permission Denied**: Verify service account has Vertex AI permissions
3. **Dataset Not Found**: Check if upload completed successfully

### **Support Resources**
- [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)
- [Custom Training Guide](https://cloud.google.com/vertex-ai/docs/training/custom-training)
- [Pricing Calculator](https://cloud.google.com/products/calculator)

## 🎉 You're Ready!

Your Vertex AI environment is configured and ready for Singapore BERT training with your comprehensive housing dataset. Start with the setup script and customize the training pipeline for your specific needs!

**Next**: Run `python3 setup_vertex_ai.py` to begin! 🚀
